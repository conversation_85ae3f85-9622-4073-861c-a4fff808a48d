{"rustc": 14799578172954012516, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 12398300617518054423, "profile": 4317675006741846053, "path": 7353141792217271357, "deps": [[4684437522915235464, "libc", false, 10805704521223867303], [7614379793313454108, "build_script_build", false, 6675113389676150848]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-ffi-c943e0ce765bf3f5\\dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}