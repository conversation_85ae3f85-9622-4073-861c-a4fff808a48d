[package]
name = "farm-queue"
version = "0.1.0"
edition = "2021"

[dependencies]
futures = { workspace = true }
tokio = { workspace = true }
serde = { workspace = true }
tracing = { workspace = true }
log = { workspace = true }
uuid = { workspace = true, features = ["v4"] }
chrono = { workspace = true, features = ["serde"] }
async-trait = { workspace = true }
anyhow = { workspace = true }
serde_json = { workspace = true }
libp2p = { version = "0.56.0", features = [ "tokio", "gossipsub", "mdns", "noise", "macros", "tcp", "yamux", "quic", "serde", "identify"]}
crossterm = "0.27"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-appender = "0.2"
bincode = "1.3"
rand = "0.8"
sha2 = "0.10.9"
# rkyv = { version = "0.8.10", features = ["bytes-1", "smallvec-1", "uuid-1"] }
base64 = "0.22.1"
smallvec = "1.15.1"
bytes = "1.10.1"
rocksdb = "0.22.0"
flate2 = "1.0"
simplelog = "0.12"
mimalloc = "0.1.47"
sysinfo = "0.30"
whoami = "1.4"

# Raft and protobuf dependencies
raft = { version = "0.7", features = ["prost-codec"], default-features = false }
raft-proto = { version = "0.7", features = ["prost-codec"], default-features = false }
prost = "0.12"
prost-types = "0.12"

[build-dependencies]
prost-build = "0.12"
tempfile = "3.8"




