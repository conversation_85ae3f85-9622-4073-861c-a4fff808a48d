{"rustc": 14799578172954012516, "features": "[\"runtime\", \"which-rustfmt\"]", "declared_features": "[\"__cli\", \"__testing_only_extra_assertions\", \"__testing_only_libclang_16\", \"__testing_only_libclang_9\", \"default\", \"experimental\", \"logging\", \"prettyplease\", \"runtime\", \"static\", \"which-rustfmt\"]", "target": 3886691532138051063, "profile": 2225463790103693989, "path": 14762653239491293504, "deps": [[950716570147248582, "cexpr", false, 18242477602941054656], [3060637413840920116, "proc_macro2", false, 17861231915632534772], [3317542222502007281, "itertools", false, 17788376714576916353], [4885725550624711673, "clang_sys", false, 9936355224651141020], [4974441333307933176, "syn", false, 5125971225996987035], [7896293946984509699, "bitflags", false, 12224930908667793411], [8410525223747752176, "shlex", false, 11857247705523502006], [8519698367355478612, "build_script_build", false, 11085475436484111804], [9451456094439810778, "regex", false, 10664981519166830513], [17990358020177143287, "quote", false, 16547450557769863501], [18335655851112826545, "rustc_hash", false, 15689160184049072269]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bindgen-062633adcaca8873\\dep-lib-bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}