use rkyv::{Archive, Deserialize as RkyvDeserialize, Serialize as RkyvSerialize};
use libp2p::PeerId;
use std::collections::{HashMap, HashSet};
use std::time::{Duration, Instant};
use rand::Rng;

#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum PaxosRole {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>Proposer, // Similar to <PERSON><PERSON>'s Leader
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, PartialOrd, Ord, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct ProposalNumber {
    pub round: u64,
    pub proposer_id: [u8; 32], // PeerId as bytes for serialization
}

impl ProposalNumber {
    pub fn new(round: u64, proposer_id: PeerId) -> Self {
        let mut id_bytes = [0u8; 32];
        let peer_bytes = proposer_id.to_bytes();
        id_bytes[..peer_bytes.len().min(32)].copy_from_slice(&peer_bytes[..peer_bytes.len().min(32)]);
        
        Self {
            round,
            proposer_id: id_bytes,
        }
    }
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct PaxosValue {
    pub sequence: u64,
    pub command: LogCommand,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub enum LogCommand {
    AssignTask { task_id: [u8; 16], worker: Vec<u8> },
    UpdateTaskStatus { task_id: [u8; 16], status: crate::types::TaskStatus },
}

// Phase 1 Messages
#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct Prepare {
    pub proposal_number: ProposalNumber,
    pub sequence: u64,
    pub proposer_id: Vec<u8>,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct Promise {
    pub proposal_number: ProposalNumber,
    pub sequence: u64,
    pub acceptor_id: Vec<u8>,
    pub last_accepted_proposal: Option<ProposalNumber>,
    pub last_accepted_value: Option<PaxosValue>,
}

// Phase 2 Messages
#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct Accept {
    pub proposal_number: ProposalNumber,
    pub sequence: u64,
    pub proposer_id: Vec<u8>,
    pub value: PaxosValue,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct Accepted {
    pub proposal_number: ProposalNumber,
    pub sequence: u64,
    pub acceptor_id: Vec<u8>,
    pub value: PaxosValue,
}

// Distinguished Proposer Election Messages
#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct ProposeLeader {
    pub proposer_id: Vec<u8>,
    pub proposal_number: ProposalNumber,
    pub last_committed_sequence: u64,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct AckLeader {
    pub proposer_id: Vec<u8>,
    pub acceptor_id: Vec<u8>,
    pub proposal_number: ProposalNumber,
}

#[derive(Debug, Clone)]
pub struct PaxosInstance {
    pub sequence: u64,
    pub promised_proposal: Option<ProposalNumber>,
    pub accepted_proposal: Option<ProposalNumber>,
    pub accepted_value: Option<PaxosValue>,
    pub promises: HashMap<PeerId, Promise>,
    pub accepts: HashMap<PeerId, Accepted>,
}

impl PaxosInstance {
    pub fn new(sequence: u64) -> Self {
        Self {
            sequence,
            promised_proposal: None,
            accepted_proposal: None,
            accepted_value: None,
            promises: HashMap::new(),
            accepts: HashMap::new(),
        }
    }
}

pub struct PaxosState {
    pub node_id: PeerId,
    pub role: PaxosRole,
    pub distinguished_proposer: Option<PeerId>,
    pub current_proposal_round: u64,
    pub instances: HashMap<u64, PaxosInstance>,
    pub next_sequence: u64,
    pub committed_sequence: u64,
    pub commit_log: Vec<PaxosValue>,
    
    // Distinguished proposer election
    pub leader_proposal_number: Option<ProposalNumber>,
    pub leader_acks: HashSet<PeerId>,
    pub last_heartbeat: Instant,
    pub heartbeat_timeout: Duration,
    
    // Performance optimizations
    pub is_single_node: bool,
    pub startup_grace_period: Instant,
    pub backoff_multiplier: u32,
}

impl PaxosState {
    pub fn new(node_id: PeerId) -> Self {
        let mut rng = rand::thread_rng();
        let heartbeat_timeout = Duration::from_millis(150 + rng.gen::<u64>() % 150);
        
        Self {
            node_id,
            role: PaxosRole::Acceptor,
            distinguished_proposer: None,
            current_proposal_round: 0,
            instances: HashMap::new(),
            next_sequence: 0,
            committed_sequence: 0,
            commit_log: Vec::new(),
            leader_proposal_number: None,
            leader_acks: HashSet::new(),
            last_heartbeat: Instant::now(),
            heartbeat_timeout,
            is_single_node: false,
            startup_grace_period: Instant::now() + Duration::from_secs(5),
            backoff_multiplier: 1,
        }
    }
    
    pub fn reset_heartbeat_timer(&mut self) {
        self.last_heartbeat = Instant::now();
        let mut rng = rand::thread_rng();
        self.heartbeat_timeout = Duration::from_millis(150 + rng.gen::<u64>() % 150);
    }
    
    pub fn is_heartbeat_timeout(&self) -> bool {
        Instant::now().duration_since(self.last_heartbeat) > self.heartbeat_timeout
    }
    
    pub fn should_become_distinguished_proposer(&self, connected_peers: usize) -> bool {
        // Single node optimization
        connected_peers == 0 && self.distinguished_proposer.is_none()
    }
    
    pub fn become_distinguished_proposer(&mut self) {
        self.role = PaxosRole::DistinguishedProposer;
        self.distinguished_proposer = Some(self.node_id);
        self.current_proposal_round += 1;
        self.leader_proposal_number = Some(ProposalNumber::new(self.current_proposal_round, self.node_id));
        self.reset_heartbeat_timer();
        log::info!("成为 Distinguished Proposer，提议轮次: {}", self.current_proposal_round);
    }
    
    pub fn start_leader_election(&mut self) {
        if Instant::now() < self.startup_grace_period {
            log::debug!("在启动宽限期内，跳过领导者选举");
            return;
        }
        
        self.role = PaxosRole::Proposer;
        self.current_proposal_round += self.backoff_multiplier as u64;
        self.leader_proposal_number = Some(ProposalNumber::new(self.current_proposal_round, self.node_id));
        self.leader_acks.clear();
        self.leader_acks.insert(self.node_id);
        self.reset_heartbeat_timer();
        log::info!("开始领导者选举，提议轮次: {}", self.current_proposal_round);
    }
    
    pub fn become_follower(&mut self, distinguished_proposer: Option<PeerId>, proposal_number: Option<ProposalNumber>) {
        self.role = PaxosRole::Acceptor;
        self.distinguished_proposer = distinguished_proposer;
        self.leader_proposal_number = proposal_number;
        self.leader_acks.clear();
        self.reset_heartbeat_timer();
        self.backoff_multiplier = 1; // Reset backoff on successful leader
        
        if let Some(proposer) = distinguished_proposer {
            log::info!("成为 Acceptor，Distinguished Proposer: {}", proposer);
        }
    }
    
    pub fn get_or_create_instance(&mut self, sequence: u64) -> &mut PaxosInstance {
        self.instances.entry(sequence).or_insert_with(|| PaxosInstance::new(sequence))
    }
    
    pub fn create_prepare(&mut self, sequence: u64) -> Prepare {
        self.current_proposal_round += 1;
        let proposal_number = ProposalNumber::new(self.current_proposal_round, self.node_id);
        
        Prepare {
            proposal_number,
            sequence,
            proposer_id: self.node_id.to_bytes(),
        }
    }
    
    pub fn handle_prepare(&mut self, prepare: Prepare) -> Option<Promise> {
        let instance = self.get_or_create_instance(prepare.sequence);
        
        // Check if we should promise this proposal
        if instance.promised_proposal.is_none() || 
           prepare.proposal_number > instance.promised_proposal.unwrap() {
            instance.promised_proposal = Some(prepare.proposal_number);
            
            Some(Promise {
                proposal_number: prepare.proposal_number,
                sequence: prepare.sequence,
                acceptor_id: self.node_id.to_bytes(),
                last_accepted_proposal: instance.accepted_proposal,
                last_accepted_value: instance.accepted_value.clone(),
            })
        } else {
            None
        }
    }
    
    pub fn handle_promise(&mut self, promise: Promise) -> Option<Accept> {
        if self.role != PaxosRole::DistinguishedProposer {
            return None;
        }
        
        let instance = self.get_or_create_instance(promise.sequence);
        if let Ok(acceptor_id) = PeerId::from_bytes(&promise.acceptor_id) {
            instance.promises.insert(acceptor_id, promise.clone());
        }
        
        // Check if we have majority
        let total_nodes = instance.promises.len() + 1; // +1 for self
        let majority = (total_nodes / 2) + 1;
        
        if instance.promises.len() >= majority - 1 { // -1 because we count ourselves
            // Choose value: either highest accepted value or new value
            let value = instance.promises.values()
                .filter_map(|p| p.last_accepted_value.clone())
                .max_by_key(|v| v.sequence)
                .unwrap_or_else(|| {
                    // Create new value if none accepted
                    PaxosValue {
                        sequence: promise.sequence,
                        command: LogCommand::AssignTask {
                            task_id: [0; 16], // Placeholder
                            worker: vec![],
                        },
                    }
                });
            
            Some(Accept {
                proposal_number: promise.proposal_number,
                sequence: promise.sequence,
                proposer_id: self.node_id.to_bytes(),
                value,
            })
        } else {
            None
        }
    }
    
    pub fn handle_accept(&mut self, accept: Accept) -> Option<Accepted> {
        let instance = self.get_or_create_instance(accept.sequence);
        
        // Check if we can accept this proposal
        if instance.promised_proposal.is_none() || 
           accept.proposal_number >= instance.promised_proposal.unwrap() {
            instance.accepted_proposal = Some(accept.proposal_number);
            instance.accepted_value = Some(accept.value.clone());
            
            Some(Accepted {
                proposal_number: accept.proposal_number,
                sequence: accept.sequence,
                acceptor_id: self.node_id.to_bytes(),
                value: accept.value,
            })
        } else {
            None
        }
    }
    
    pub fn handle_accepted(&mut self, accepted: Accepted) -> bool {
        let instance = self.get_or_create_instance(accepted.sequence);
        if let Ok(acceptor_id) = PeerId::from_bytes(&accepted.acceptor_id) {
            instance.accepts.insert(acceptor_id, accepted.clone());
        }
        
        // Check if value is committed (majority accepted)
        let total_nodes = instance.accepts.len() + 1;
        let majority = (total_nodes / 2) + 1;
        
        if instance.accepts.len() >= majority - 1 {
            // Commit the value
            if accepted.sequence == self.committed_sequence + 1 {
                self.committed_sequence = accepted.sequence;
                self.commit_log.push(accepted.value.clone());
                self.next_sequence = accepted.sequence + 1;
                return true;
            }
        }
        
        false
    }
    
    pub fn handle_leader_timeout(&mut self) {
        if self.role != PaxosRole::DistinguishedProposer && self.is_heartbeat_timeout() {
            self.backoff_multiplier = self.backoff_multiplier.saturating_mul(2).min(32);
            self.start_leader_election();
        }
    }
}

// Persistence support
impl PaxosState {
    pub fn get_persistent_state(&self) -> (u64, Option<ProposalNumber>, u64) {
        (
            self.current_proposal_round,
            self.leader_proposal_number,
            self.committed_sequence,
        )
    }
    
    pub fn restore_from_persistent_state(
        &mut self,
        round: u64,
        leader_proposal: Option<ProposalNumber>,
        committed_seq: u64,
    ) {
        self.current_proposal_round = round;
        self.leader_proposal_number = leader_proposal;
        self.committed_sequence = committed_seq;
    }
}