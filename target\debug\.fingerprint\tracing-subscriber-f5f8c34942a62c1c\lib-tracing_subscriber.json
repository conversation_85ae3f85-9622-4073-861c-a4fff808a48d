{"rustc": 14799578172954012516, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 6355579909791343455, "path": 12864320166293439298, "deps": [[1009387600818341822, "matchers", false, 17034734254355032667], [1017461770342116999, "sharded_slab", false, 16517789703294104498], [1359731229228270592, "thread_local", false, 81888448109274289], [3424551429995674438, "tracing_core", false, 3408457157823889315], [3666196340704888985, "smallvec", false, 9056791839881781258], [3722963349756955755, "once_cell", false, 13393876230255124741], [6981130804689348050, "tracing_serde", false, 9444810584636387298], [8569119365930580996, "serde_json", false, 12877272783564417186], [8606274917505247608, "tracing", false, 11094703943940771815], [8614575489689151157, "nu_ansi_term", false, 14571611520575224339], [9451456094439810778, "regex", false, 2654517255840822446], [9689903380558560274, "serde", false, 4149794575848233682], [10806489435541507125, "tracing_log", false, 13672125984141237466]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-f5f8c34942a62c1c\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}