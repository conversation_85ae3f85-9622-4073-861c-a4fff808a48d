{"rustc": 14799578172954012516, "features": "[\"ring\", \"rustls-ring\"]", "declared_features": "[\"arbitrary\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"bloom\", \"default\", \"log\", \"platform-verifier\", \"ring\", \"rustls\", \"rustls-aws-lc-rs\", \"rustls-aws-lc-rs-fips\", \"rustls-log\", \"rustls-ring\"]", "target": 1396233574809871990, "profile": 11250625435679592442, "path": 18052148993786679825, "deps": [[1003879549763726989, "lru_slab", false, 2295185668877406267], [1042707345065476716, "tinyvec", false, 8968729162674231294], [5451793922601807560, "slab", false, 4368066700314686702], [5491919304041016563, "ring", false, 6946860015157530409], [8606274917505247608, "tracing", false, 11094703943940771815], [10806645703491011684, "thiserror", false, 12398725825002675698], [11916940916964035392, "rand", false, 9286351777454675946], [14619257664405537057, "rustls", false, 4837973330981708235], [16066129441945555748, "bytes", false, 13911795451722933334], [18335655851112826545, "rustc_hash", false, 5863816331446046105]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\quinn-proto-a411ce4a91efd3ac\\dep-lib-quinn_proto", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}