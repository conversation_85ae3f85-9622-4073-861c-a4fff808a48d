{"rustc": 14799578172954012516, "features": "[\"ring\", \"std\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 17056000663290198991, "deps": [[2883436298747778685, "pki_types", false, 15195193559197903087], [3722963349756955755, "once_cell", false, 13393876230255124741], [5491919304041016563, "ring", false, 6946860015157530409], [6528079939221783635, "zeroize", false, 17367905892630736986], [8151164558401866693, "<PERSON><PERSON><PERSON>", false, 9759630069898995657], [14619257664405537057, "build_script_build", false, 7726100473908200389], [17003143334332120809, "subtle", false, 18383335943724491122]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-ab4c5858f3183f17\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}