{"rustc": 14799578172954012516, "features": "[\"std\", \"unicode-bool\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2225463790103693989, "path": 17810625199745679432, "deps": [[555019317135488525, "regex_automata", false, 17732933471013428041], [9408802513701742484, "regex_syntax", false, 2447430663303605856]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-63bf84ee0dfd4963\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}