use rkyv::{Archive, Deserialize as RkyvDeserialize, Serialize as RkyvSerialize};
use std::collections::HashMap;
use crate::{paxos::{Prepare, Promise, Accept, Accepted, ProposeLeader, AckLeader}, types::{Task, DeviceInfo}};

#[derive(Debu<PERSON>, <PERSON>lone, Archive, RkyvDeserialize, RkyvSerialize)]
pub enum QueueMessage {
    // Task messages
    NewTask(Task),
    UpdateTask { task: Task },
    DeleteTask { task_id: [u8; 16], version: u64 },
    AssignTask { task_id: [u8; 16], worker: Vec<u8>, version: u64 },
    CompleteTask { task_id: [u8; 16], worker: Vec<u8>, version: u64 },
    FailTask { task_id: [u8; 16], worker: Vec<u8>, reason: String, version: u64 },
    RequestTask { worker: Vec<u8> },
    
    // Paxos messages (replacing <PERSON><PERSON>)
    Prepare(Prepare),
    Promise(Promise),
    Accept(Accept),
    Accepted(Accepted),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(ProposeLeader),
    <PERSON>ck<PERSON>ead<PERSON>(AckLeader),
    
    // Sync messages
    Heartbeat { leader_id: Vec<u8>, proposal_number: u64 },
    QueryTaskStatus { task_id: [u8; 16] },
    TaskStatusResponse { task: Task },
    SyncRequest { from: Vec<u8>, request_id: [u8; 16] },
    SyncResponse { tasks: Vec<Task>, request_id: [u8; 16] },
    TaskStatusBroadcast { task: Task },
    HashRequest { from: Vec<u8> },
    HashResponse { from: Vec<u8>, task_hashes: HashMap<[u8; 16], u64> },
    
    // 设备信息消息
    DeviceInfoRequest { from: Vec<u8> },
    DeviceInfoResponse { peer_id: Vec<u8>, device_info: DeviceInfo },
}

impl QueueMessage {
    pub fn serialize(&self) -> Result<Vec<u8>, rkyv::rancor::Error> {
        rkyv::to_bytes(self).map(|aligned_vec| aligned_vec.into_vec())
    }
    
    pub fn deserialize(data: &[u8]) -> Result<QueueMessage, Box<dyn std::error::Error>> {
        let archived = rkyv::from_bytes::<QueueMessage, rkyv::rancor::Error>(data)?;
        Ok(archived)
    }
}