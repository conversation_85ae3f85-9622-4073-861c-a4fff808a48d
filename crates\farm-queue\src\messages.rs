use prost::Message;
use std::collections::HashMap;
use libp2p::PeerId;

// 引入生成的 protobuf 代码
pub mod proto {
    include!(concat!(env!("OUT_DIR"), "/farm_queue.rs"));
}

use proto::*;

// 为 protobuf 生成的类型添加辅助方法
impl Task {
    pub fn new(task_type: String, payload: String) -> Self {
        let now = chrono::Utc::now();
        let id = uuid::Uuid::new_v4();
        
        Self {
            id: id.as_bytes().to_vec(),
            task_type,
            priority: 0,
            payload,
            status: Some(TaskStatus {
                status_type: TaskStatusType::Pending as i32,
                worker: vec![],
                reason: String::new(),
            }),
            created_at: now.timestamp(),
            updated_at: now.timestamp(),
            retry_count: 0,
            max_retries: 3,
            version: 0,
        }
    }
    
    pub fn with_priority(task_type: String, payload: String, priority: u16) -> Self {
        let mut task = Self::new(task_type, payload);
        task.priority = priority as u32;
        task
    }
    
    pub fn get_uuid(&self) -> Result<uuid::Uuid, Box<dyn std::error::Error>> {
        if self.id.len() == 16 {
            let mut bytes = [0u8; 16];
            bytes.copy_from_slice(&self.id);
            Ok(uuid::Uuid::from_bytes(bytes))
        } else {
            Err("Invalid UUID length".into())
        }
    }
    
    pub fn is_pending(&self) -> bool {
        match &self.status {
            Some(status) => status.status_type == TaskStatusType::Pending as i32,
            None => false,
        }
    }
    
    pub fn is_processing(&self) -> bool {
        match &self.status {
            Some(status) => status.status_type == TaskStatusType::Processing as i32,
            None => false,
        }
    }
    
    pub fn is_completed(&self) -> bool {
        match &self.status {
            Some(status) => status.status_type == TaskStatusType::Completed as i32,
            None => false,
        }
    }
    
    pub fn is_failed(&self) -> bool {
        match &self.status {
            Some(status) => status.status_type == TaskStatusType::Failed as i32,
            None => false,
        }
    }
}

// QueueMessage 序列化和反序列化辅助方法
impl QueueMessage {
    pub fn serialize(&self) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        let mut buf = Vec::new();
        self.encode(&mut buf)?;
        Ok(buf)
    }
    
    pub fn deserialize(data: &[u8]) -> Result<QueueMessage, Box<dyn std::error::Error>> {
        Ok(QueueMessage::decode(data)?)
    }
    
    // 辅助构造函数
    pub fn new_task(task: Task) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::NewTask(NewTaskMessage { task: Some(task) })),
        }
    }
    
    pub fn update_task(task: Task) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::UpdateTask(UpdateTaskMessage { task: Some(task) })),
        }
    }
    
    pub fn delete_task(task_id: Vec<u8>, version: u64) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::DeleteTask(DeleteTaskMessage { task_id, version })),
        }
    }
    
    pub fn assign_task(task_id: Vec<u8>, worker: Vec<u8>, version: u64) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::AssignTask(AssignTaskMessage { 
                task_id, 
                worker, 
                version 
            })),
        }
    }
    
    pub fn complete_task(task_id: Vec<u8>, worker: Vec<u8>, version: u64) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::CompleteTask(CompleteTaskMessage { 
                task_id, 
                worker, 
                version 
            })),
        }
    }
    
    pub fn fail_task(task_id: Vec<u8>, worker: Vec<u8>, reason: String, version: u64) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::FailTask(FailTaskMessage { 
                task_id, 
                worker, 
                reason, 
                version 
            })),
        }
    }
    
    pub fn request_task(worker: Vec<u8>) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::RequestTask(RequestTaskMessage { worker })),
        }
    }
    
    pub fn heartbeat(leader_id: Vec<u8>, term: u64) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::Heartbeat(HeartbeatMessage { 
                leader_id, 
                term 
            })),
        }
    }
    
    pub fn task_status_broadcast(task: Task) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::TaskStatusBroadcast(
                TaskStatusBroadcastMessage { task: Some(task) }
            )),
        }
    }
    
    pub fn device_info_request(from: Vec<u8>) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::DeviceInfoRequest(
                DeviceInfoRequestMessage { from }
            )),
        }
    }
    
    pub fn device_info_response(peer_id: Vec<u8>, device_info: DeviceInfo) -> Self {
        QueueMessage {
            message: Some(queue_message::Message::DeviceInfoResponse(
                DeviceInfoResponseMessage { 
                    peer_id, 
                    device_info: Some(device_info) 
                }
            )),
        }
    }
}

// 兼容性类型转换
pub fn convert_task_status_to_crate(proto_status: &TaskStatus) -> crate::types::TaskStatus {
    match TaskStatusType::try_from(proto_status.status_type).unwrap_or(TaskStatusType::Pending) {
        TaskStatusType::Pending => crate::types::TaskStatus::Pending,
        TaskStatusType::Processing => crate::types::TaskStatus::Processing { 
            worker: proto_status.worker.clone() 
        },
        TaskStatusType::Completed => crate::types::TaskStatus::Completed,
        TaskStatusType::Failed => crate::types::TaskStatus::Failed { 
            reason: proto_status.reason.clone() 
        },
    }
}

pub fn convert_task_status_from_crate(status: &crate::types::TaskStatus) -> TaskStatus {
    match status {
        crate::types::TaskStatus::Pending => TaskStatus {
            status_type: TaskStatusType::Pending as i32,
            worker: vec![],
            reason: String::new(),
        },
        crate::types::TaskStatus::Processing { worker } => TaskStatus {
            status_type: TaskStatusType::Processing as i32,
            worker: worker.clone(),
            reason: String::new(),
        },
        crate::types::TaskStatus::Completed => TaskStatus {
            status_type: TaskStatusType::Completed as i32,
            worker: vec![],
            reason: String::new(),
        },
        crate::types::TaskStatus::Failed { reason } => TaskStatus {
            status_type: TaskStatusType::Failed as i32,
            worker: vec![],
            reason: reason.clone(),
        },
    }
}

// Task 和 crate::types::Task 之间的转换
pub fn convert_task_to_proto(task: &crate::types::Task) -> Task {
    Task {
        id: task.id.to_vec(),
        task_type: task.task_type.clone(),
        priority: task.priority as u32,
        payload: task.payload.clone(),
        status: Some(convert_task_status_from_crate(&task.status)),
        created_at: task.created_at,
        updated_at: task.updated_at,
        retry_count: task.retry_count,
        max_retries: task.max_retries,
        version: task.version,
    }
}

pub fn convert_task_from_proto(proto_task: &Task) -> Result<crate::types::Task, Box<dyn std::error::Error>> {
    let mut id = [0u8; 16];
    if proto_task.id.len() != 16 {
        return Err("Invalid task ID length".into());
    }
    id.copy_from_slice(&proto_task.id);
    
    let status = proto_task.status.as_ref()
        .map(convert_task_status_to_crate)
        .unwrap_or(crate::types::TaskStatus::Pending);
    
    Ok(crate::types::Task {
        id,
        task_type: proto_task.task_type.clone(),
        priority: proto_task.priority as u16,
        payload: proto_task.payload.clone(),
        status,
        created_at: proto_task.created_at,
        updated_at: proto_task.updated_at,
        retry_count: proto_task.retry_count,
        max_retries: proto_task.max_retries,
        version: proto_task.version,
    })
}