2025/07/26-18:47:11.778557 754 RocksDB version: 8.10.0
2025/07/26-18:47:11.779047 754 Compile date 2023-12-15 13:01:14
2025/07/26-18:47:11.779058 754 DB SUMMARY
2025/07/26-18:47:11.779065 754 Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-18:47:11.779071 754 DB Session ID:  QCPA6J0RY9D0ZXU95K0J
2025/07/26-18:47:11.779206 754 SST files in ./data/12D3KooWEvBGM2LvHd9EHUY4s4mAupMgcD98P2Ffy8mjyLdDLF3T dir, Total Num: 0, files: 
2025/07/26-18:47:11.779215 754 Write Ahead Log file in ./data/12D3KooWEvBGM2LvHd9EHUY4s4mAupMgcD98P2Ffy8mjyLdDLF3T: 
2025/07/26-18:47:11.779221 754                         Options.error_if_exists: 0
2025/07/26-18:47:11.779226 754                       Options.create_if_missing: 1
2025/07/26-18:47:11.779231 754                         Options.paranoid_checks: 1
2025/07/26-18:47:11.779359 754             Options.flush_verify_memtable_count: 1
2025/07/26-18:47:11.779366 754          Options.compaction_verify_record_count: 1
2025/07/26-18:47:11.779368 754                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-18:47:11.779370 754        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-18:47:11.779372 754                                     Options.env: 00000182A3452720
2025/07/26-18:47:11.779375 754                                      Options.fs: WinFS
2025/07/26-18:47:11.779377 754                                Options.info_log: 00000182A33BDE70
2025/07/26-18:47:11.779378 754                Options.max_file_opening_threads: 16
2025/07/26-18:47:11.779380 754                              Options.statistics: 0000000000000000
2025/07/26-18:47:11.779382 754                               Options.use_fsync: 0
2025/07/26-18:47:11.779384 754                       Options.max_log_file_size: 0
2025/07/26-18:47:11.779386 754                  Options.max_manifest_file_size: 1073741824
2025/07/26-18:47:11.779387 754                   Options.log_file_time_to_roll: 0
2025/07/26-18:47:11.779389 754                       Options.keep_log_file_num: 1000
2025/07/26-18:47:11.779391 754                    Options.recycle_log_file_num: 0
2025/07/26-18:47:11.779393 754                         Options.allow_fallocate: 1
2025/07/26-18:47:11.779394 754                        Options.allow_mmap_reads: 0
2025/07/26-18:47:11.779396 754                       Options.allow_mmap_writes: 0
2025/07/26-18:47:11.779398 754                        Options.use_direct_reads: 0
2025/07/26-18:47:11.779400 754                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-18:47:11.779401 754          Options.create_missing_column_families: 1
2025/07/26-18:47:11.779403 754                              Options.db_log_dir: 
2025/07/26-18:47:11.779405 754                                 Options.wal_dir: 
2025/07/26-18:47:11.779407 754                Options.table_cache_numshardbits: 6
2025/07/26-18:47:11.779408 754                         Options.WAL_ttl_seconds: 0
2025/07/26-18:47:11.779410 754                       Options.WAL_size_limit_MB: 0
2025/07/26-18:47:11.779412 754                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-18:47:11.779414 754             Options.manifest_preallocation_size: 4194304
2025/07/26-18:47:11.779415 754                     Options.is_fd_close_on_exec: 1
2025/07/26-18:47:11.779417 754                   Options.advise_random_on_open: 1
2025/07/26-18:47:11.779419 754                    Options.db_write_buffer_size: 0
2025/07/26-18:47:11.779421 754                    Options.write_buffer_manager: 00000182A3451DC0
2025/07/26-18:47:11.779422 754         Options.access_hint_on_compaction_start: 1
2025/07/26-18:47:11.779424 754           Options.random_access_max_buffer_size: 1048576
2025/07/26-18:47:11.779426 754                      Options.use_adaptive_mutex: 0
2025/07/26-18:47:11.779427 754                            Options.rate_limiter: 0000000000000000
2025/07/26-18:47:11.779429 754     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-18:47:11.779431 754                       Options.wal_recovery_mode: 2
2025/07/26-18:47:11.779433 754                  Options.enable_thread_tracking: 0
2025/07/26-18:47:11.779456 754                  Options.enable_pipelined_write: 0
2025/07/26-18:47:11.779459 754                  Options.unordered_write: 0
2025/07/26-18:47:11.779461 754         Options.allow_concurrent_memtable_write: 1
2025/07/26-18:47:11.779463 754      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-18:47:11.779464 754             Options.write_thread_max_yield_usec: 100
2025/07/26-18:47:11.779466 754            Options.write_thread_slow_yield_usec: 3
2025/07/26-18:47:11.779468 754                               Options.row_cache: None
2025/07/26-18:47:11.779469 754                              Options.wal_filter: None
2025/07/26-18:47:11.779471 754             Options.avoid_flush_during_recovery: 0
2025/07/26-18:47:11.779473 754             Options.allow_ingest_behind: 0
2025/07/26-18:47:11.779475 754             Options.two_write_queues: 0
2025/07/26-18:47:11.779476 754             Options.manual_wal_flush: 0
2025/07/26-18:47:11.779478 754             Options.wal_compression: 0
2025/07/26-18:47:11.779480 754             Options.atomic_flush: 0
2025/07/26-18:47:11.779481 754             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-18:47:11.779483 754                 Options.persist_stats_to_disk: 0
2025/07/26-18:47:11.779485 754                 Options.write_dbid_to_manifest: 0
2025/07/26-18:47:11.779487 754                 Options.log_readahead_size: 0
2025/07/26-18:47:11.779488 754                 Options.file_checksum_gen_factory: Unknown
2025/07/26-18:47:11.779490 754                 Options.best_efforts_recovery: 0
2025/07/26-18:47:11.779492 754                Options.max_bgerror_resume_count: 2147483647
2025/07/26-18:47:11.779494 754            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-18:47:11.779495 754             Options.allow_data_in_errors: 0
2025/07/26-18:47:11.779497 754             Options.db_host_id: __hostname__
2025/07/26-18:47:11.779499 754             Options.enforce_single_del_contracts: true
2025/07/26-18:47:11.779501 754             Options.max_background_jobs: 4
2025/07/26-18:47:11.779502 754             Options.max_background_compactions: -1
2025/07/26-18:47:11.779504 754             Options.max_subcompactions: 1
2025/07/26-18:47:11.779506 754             Options.avoid_flush_during_shutdown: 0
2025/07/26-18:47:11.779508 754           Options.writable_file_max_buffer_size: 1048576
2025/07/26-18:47:11.779509 754             Options.delayed_write_rate : 16777216
2025/07/26-18:47:11.779511 754             Options.max_total_wal_size: 0
2025/07/26-18:47:11.779513 754             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-18:47:11.779515 754                   Options.stats_dump_period_sec: 600
2025/07/26-18:47:11.779516 754                 Options.stats_persist_period_sec: 600
2025/07/26-18:47:11.779518 754                 Options.stats_history_buffer_size: 1048576
2025/07/26-18:47:11.779520 754                          Options.max_open_files: 1000
2025/07/26-18:47:11.779522 754                          Options.bytes_per_sync: 0
2025/07/26-18:47:11.779524 754                      Options.wal_bytes_per_sync: 0
2025/07/26-18:47:11.779526 754                   Options.strict_bytes_per_sync: 0
2025/07/26-18:47:11.779527 754       Options.compaction_readahead_size: 2097152
2025/07/26-18:47:11.779529 754                  Options.max_background_flushes: -1
2025/07/26-18:47:11.779531 754 Options.daily_offpeak_time_utc: 
2025/07/26-18:47:11.779533 754 Compression algorithms supported:
2025/07/26-18:47:11.779538 754 	kZSTD supported: 1
2025/07/26-18:47:11.779540 754 	kSnappyCompression supported: 1
2025/07/26-18:47:11.779542 754 	kBZip2Compression supported: 1
2025/07/26-18:47:11.779543 754 	kZlibCompression supported: 1
2025/07/26-18:47:11.779545 754 	kLZ4Compression supported: 1
2025/07/26-18:47:11.779547 754 	kXpressCompression supported: 0
2025/07/26-18:47:11.779549 754 	kLZ4HCCompression supported: 1
2025/07/26-18:47:11.779550 754 	kZSTDNotFinalCompression supported: 1
2025/07/26-18:47:11.779554 754 Fast CRC32 supported: Not supported on x86
2025/07/26-18:47:11.779570 754 DMutex implementation: std::mutex
2025/07/26-18:47:11.786007 754 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-18:47:11.796078 754 [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWEvBGM2LvHd9EHUY4s4mAupMgcD98P2Ffy8mjyLdDLF3T/MANIFEST-000001
2025/07/26-18:47:11.796229 754 [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-18:47:11.796233 754               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:47:11.796235 754           Options.merge_operator: None
2025/07/26-18:47:11.796237 754        Options.compaction_filter: None
2025/07/26-18:47:11.796239 754        Options.compaction_filter_factory: None
2025/07/26-18:47:11.796240 754  Options.sst_partitioner_factory: None
2025/07/26-18:47:11.796242 754         Options.memtable_factory: SkipListFactory
2025/07/26-18:47:11.796244 754            Options.table_factory: BlockBasedTable
2025/07/26-18:47:11.796275 754            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000182A3423620)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000182A3452A00
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:47:11.796277 754        Options.write_buffer_size: 67108864
2025/07/26-18:47:11.796279 754  Options.max_write_buffer_number: 2
2025/07/26-18:47:11.796281 754          Options.compression: Snappy
2025/07/26-18:47:11.796282 754                  Options.bottommost_compression: Disabled
2025/07/26-18:47:11.796284 754       Options.prefix_extractor: nullptr
2025/07/26-18:47:11.796286 754   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:47:11.796288 754             Options.num_levels: 7
2025/07/26-18:47:11.796289 754        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:47:11.796295 754     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:47:11.796298 754     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:47:11.796299 754            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:47:11.796301 754                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:47:11.796303 754               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:47:11.796305 754         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:47:11.796308 754         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:47:11.796309 754         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:47:11.796311 754                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:47:11.796313 754         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:47:11.796315 754         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:47:11.796317 754            Options.compression_opts.window_bits: -14
2025/07/26-18:47:11.796319 754                  Options.compression_opts.level: 32767
2025/07/26-18:47:11.796320 754               Options.compression_opts.strategy: 0
2025/07/26-18:47:11.796324 754         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:47:11.796326 754         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:47:11.796328 754         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:47:11.796330 754         Options.compression_opts.parallel_threads: 1
2025/07/26-18:47:11.796332 754                  Options.compression_opts.enabled: false
2025/07/26-18:47:11.796334 754         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:47:11.796335 754      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:47:11.796337 754          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:47:11.796339 754              Options.level0_stop_writes_trigger: 36
2025/07/26-18:47:11.796341 754                   Options.target_file_size_base: 67108864
2025/07/26-18:47:11.796342 754             Options.target_file_size_multiplier: 1
2025/07/26-18:47:11.796344 754                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:47:11.796346 754 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:47:11.796348 754          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:47:11.796350 754 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:47:11.796352 754 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:47:11.796353 754 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:47:11.796355 754 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:47:11.796357 754 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:47:11.796358 754 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:47:11.796360 754 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:47:11.796362 754       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:47:11.796364 754                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:47:11.796365 754   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:47:11.796367 754                        Options.arena_block_size: 1048576
2025/07/26-18:47:11.796369 754   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:47:11.796371 754   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:47:11.796372 754                Options.disable_auto_compactions: 0
2025/07/26-18:47:11.796375 754                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:47:11.796378 754                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:47:11.796380 754 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:47:11.796382 754 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:47:11.796384 754 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:47:11.796385 754 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:47:11.796387 754 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:47:11.796389 754 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:47:11.796391 754 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:47:11.796393 754 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:47:11.796397 754                   Options.table_properties_collectors: 
2025/07/26-18:47:11.796399 754                   Options.inplace_update_support: 0
2025/07/26-18:47:11.796401 754                 Options.inplace_update_num_locks: 10000
2025/07/26-18:47:11.796403 754               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:47:11.796405 754               Options.memtable_whole_key_filtering: 0
2025/07/26-18:47:11.796406 754   Options.memtable_huge_page_size: 0
2025/07/26-18:47:11.796408 754                           Options.bloom_locality: 0
2025/07/26-18:47:11.796410 754                    Options.max_successive_merges: 0
2025/07/26-18:47:11.796411 754                Options.optimize_filters_for_hits: 0
2025/07/26-18:47:11.796438 754                Options.paranoid_file_checks: 0
2025/07/26-18:47:11.796440 754                Options.force_consistency_checks: 1
2025/07/26-18:47:11.796442 754                Options.report_bg_io_stats: 0
2025/07/26-18:47:11.796444 754                               Options.ttl: 2592000
2025/07/26-18:47:11.796445 754          Options.periodic_compaction_seconds: 0
2025/07/26-18:47:11.796447 754                        Options.default_temperature: kUnknown
2025/07/26-18:47:11.796449 754  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:47:11.796451 754    Options.preserve_internal_time_seconds: 0
2025/07/26-18:47:11.796452 754                       Options.enable_blob_files: false
2025/07/26-18:47:11.796454 754                           Options.min_blob_size: 0
2025/07/26-18:47:11.796456 754                          Options.blob_file_size: 268435456
2025/07/26-18:47:11.796458 754                   Options.blob_compression_type: NoCompression
2025/07/26-18:47:11.796459 754          Options.enable_blob_garbage_collection: false
2025/07/26-18:47:11.796461 754      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:47:11.796463 754 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:47:11.796465 754          Options.blob_compaction_readahead_size: 0
2025/07/26-18:47:11.796467 754                Options.blob_file_starting_level: 0
2025/07/26-18:47:11.796468 754         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:47:11.796470 754            Options.memtable_max_range_deletions: 0
2025/07/26-18:47:11.797240 754 [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWEvBGM2LvHd9EHUY4s4mAupMgcD98P2Ffy8mjyLdDLF3T/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-18:47:11.797249 754 [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-18:47:11.797459 754 [db/db_impl/db_impl_open.cc:646] DB ID: e207895b-6a0d-11f0-98b6-d4e98a1a402d
2025/07/26-18:47:11.798318 754 [db/version_set.cc:5439] Creating manifest 5
2025/07/26-18:47:11.808442 754 [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-18:47:11.808461 754               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:47:11.808464 754           Options.merge_operator: None
2025/07/26-18:47:11.808466 754        Options.compaction_filter: None
2025/07/26-18:47:11.808467 754        Options.compaction_filter_factory: None
2025/07/26-18:47:11.808469 754  Options.sst_partitioner_factory: None
2025/07/26-18:47:11.808471 754         Options.memtable_factory: SkipListFactory
2025/07/26-18:47:11.808473 754            Options.table_factory: BlockBasedTable
2025/07/26-18:47:11.808503 754            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000182A3429290)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000182A3452AF0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:47:11.808510 754        Options.write_buffer_size: 67108864
2025/07/26-18:47:11.808514 754  Options.max_write_buffer_number: 2
2025/07/26-18:47:11.808516 754          Options.compression: Snappy
2025/07/26-18:47:11.808518 754                  Options.bottommost_compression: Disabled
2025/07/26-18:47:11.808520 754       Options.prefix_extractor: nullptr
2025/07/26-18:47:11.808522 754   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:47:11.808524 754             Options.num_levels: 7
2025/07/26-18:47:11.808525 754        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:47:11.808527 754     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:47:11.808529 754     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:47:11.808531 754            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:47:11.808534 754                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:47:11.808535 754               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:47:11.808537 754         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:47:11.808539 754         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:47:11.808541 754         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:47:11.808543 754                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:47:11.808544 754         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:47:11.808546 754         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:47:11.808548 754            Options.compression_opts.window_bits: -14
2025/07/26-18:47:11.808550 754                  Options.compression_opts.level: 32767
2025/07/26-18:47:11.808552 754               Options.compression_opts.strategy: 0
2025/07/26-18:47:11.808553 754         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:47:11.808555 754         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:47:11.808557 754         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:47:11.808559 754         Options.compression_opts.parallel_threads: 1
2025/07/26-18:47:11.808560 754                  Options.compression_opts.enabled: false
2025/07/26-18:47:11.808562 754         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:47:11.808565 754      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:47:11.808566 754          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:47:11.808568 754              Options.level0_stop_writes_trigger: 36
2025/07/26-18:47:11.808570 754                   Options.target_file_size_base: 67108864
2025/07/26-18:47:11.808572 754             Options.target_file_size_multiplier: 1
2025/07/26-18:47:11.808574 754                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:47:11.808576 754 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:47:11.808578 754          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:47:11.808580 754 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:47:11.808582 754 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:47:11.808584 754 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:47:11.808586 754 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:47:11.808587 754 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:47:11.808589 754 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:47:11.808591 754 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:47:11.808592 754       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:47:11.808594 754                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:47:11.808596 754   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:47:11.808598 754                        Options.arena_block_size: 1048576
2025/07/26-18:47:11.808599 754   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:47:11.808602 754   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:47:11.808604 754                Options.disable_auto_compactions: 0
2025/07/26-18:47:11.808607 754                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:47:11.808609 754                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:47:11.808611 754 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:47:11.808612 754 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:47:11.808614 754 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:47:11.808617 754 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:47:11.808618 754 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:47:11.808620 754 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:47:11.808622 754 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:47:11.808624 754 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:47:11.808631 754                   Options.table_properties_collectors: 
2025/07/26-18:47:11.808633 754                   Options.inplace_update_support: 0
2025/07/26-18:47:11.808634 754                 Options.inplace_update_num_locks: 10000
2025/07/26-18:47:11.808636 754               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:47:11.808638 754               Options.memtable_whole_key_filtering: 0
2025/07/26-18:47:11.808640 754   Options.memtable_huge_page_size: 0
2025/07/26-18:47:11.808641 754                           Options.bloom_locality: 0
2025/07/26-18:47:11.808643 754                    Options.max_successive_merges: 0
2025/07/26-18:47:11.808645 754                Options.optimize_filters_for_hits: 0
2025/07/26-18:47:11.808647 754                Options.paranoid_file_checks: 0
2025/07/26-18:47:11.808648 754                Options.force_consistency_checks: 1
2025/07/26-18:47:11.808650 754                Options.report_bg_io_stats: 0
2025/07/26-18:47:11.808652 754                               Options.ttl: 2592000
2025/07/26-18:47:11.808654 754          Options.periodic_compaction_seconds: 0
2025/07/26-18:47:11.808655 754                        Options.default_temperature: kUnknown
2025/07/26-18:47:11.808657 754  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:47:11.808659 754    Options.preserve_internal_time_seconds: 0
2025/07/26-18:47:11.808661 754                       Options.enable_blob_files: false
2025/07/26-18:47:11.808662 754                           Options.min_blob_size: 0
2025/07/26-18:47:11.808664 754                          Options.blob_file_size: 268435456
2025/07/26-18:47:11.808666 754                   Options.blob_compression_type: NoCompression
2025/07/26-18:47:11.808668 754          Options.enable_blob_garbage_collection: false
2025/07/26-18:47:11.808669 754      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:47:11.808672 754 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:47:11.808673 754          Options.blob_compaction_readahead_size: 0
2025/07/26-18:47:11.808675 754                Options.blob_file_starting_level: 0
2025/07/26-18:47:11.808677 754         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:47:11.808679 754            Options.memtable_max_range_deletions: 0
2025/07/26-18:47:11.808872 754 [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-18:47:11.811441 754 [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-18:47:11.811458 754               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:47:11.811460 754           Options.merge_operator: None
2025/07/26-18:47:11.811462 754        Options.compaction_filter: None
2025/07/26-18:47:11.811464 754        Options.compaction_filter_factory: None
2025/07/26-18:47:11.811466 754  Options.sst_partitioner_factory: None
2025/07/26-18:47:11.811469 754         Options.memtable_factory: SkipListFactory
2025/07/26-18:47:11.811473 754            Options.table_factory: BlockBasedTable
2025/07/26-18:47:11.811500 754            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000182A3428A50)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000182A3452820
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:47:11.811502 754        Options.write_buffer_size: 67108864
2025/07/26-18:47:11.811504 754  Options.max_write_buffer_number: 2
2025/07/26-18:47:11.811506 754          Options.compression: Snappy
2025/07/26-18:47:11.811508 754                  Options.bottommost_compression: Disabled
2025/07/26-18:47:11.811509 754       Options.prefix_extractor: nullptr
2025/07/26-18:47:11.811511 754   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:47:11.811513 754             Options.num_levels: 7
2025/07/26-18:47:11.811515 754        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:47:11.811516 754     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:47:11.811518 754     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:47:11.811520 754            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:47:11.811522 754                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:47:11.811523 754               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:47:11.811525 754         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:47:11.811527 754         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:47:11.811529 754         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:47:11.811531 754                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:47:11.811532 754         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:47:11.811534 754         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:47:11.811536 754            Options.compression_opts.window_bits: -14
2025/07/26-18:47:11.811538 754                  Options.compression_opts.level: 32767
2025/07/26-18:47:11.811539 754               Options.compression_opts.strategy: 0
2025/07/26-18:47:11.811541 754         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:47:11.811543 754         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:47:11.811545 754         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:47:11.811546 754         Options.compression_opts.parallel_threads: 1
2025/07/26-18:47:11.811548 754                  Options.compression_opts.enabled: false
2025/07/26-18:47:11.811550 754         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:47:11.811552 754      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:47:11.811553 754          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:47:11.811555 754              Options.level0_stop_writes_trigger: 36
2025/07/26-18:47:11.811593 754                   Options.target_file_size_base: 67108864
2025/07/26-18:47:11.811596 754             Options.target_file_size_multiplier: 1
2025/07/26-18:47:11.811598 754                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:47:11.811600 754 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:47:11.811601 754          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:47:11.811604 754 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:47:11.811605 754 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:47:11.811607 754 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:47:11.811609 754 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:47:11.811611 754 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:47:11.811612 754 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:47:11.811614 754 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:47:11.811616 754       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:47:11.811618 754                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:47:11.811619 754   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:47:11.811621 754                        Options.arena_block_size: 1048576
2025/07/26-18:47:11.811623 754   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:47:11.811625 754   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:47:11.811626 754                Options.disable_auto_compactions: 0
2025/07/26-18:47:11.811629 754                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:47:11.811631 754                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:47:11.811633 754 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:47:11.811635 754 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:47:11.811636 754 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:47:11.811638 754 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:47:11.811640 754 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:47:11.811643 754 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:47:11.811644 754 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:47:11.811646 754 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:47:11.811651 754                   Options.table_properties_collectors: 
2025/07/26-18:47:11.811653 754                   Options.inplace_update_support: 0
2025/07/26-18:47:11.811655 754                 Options.inplace_update_num_locks: 10000
2025/07/26-18:47:11.811656 754               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:47:11.811658 754               Options.memtable_whole_key_filtering: 0
2025/07/26-18:47:11.811660 754   Options.memtable_huge_page_size: 0
2025/07/26-18:47:11.811662 754                           Options.bloom_locality: 0
2025/07/26-18:47:11.811664 754                    Options.max_successive_merges: 0
2025/07/26-18:47:11.811665 754                Options.optimize_filters_for_hits: 0
2025/07/26-18:47:11.811667 754                Options.paranoid_file_checks: 0
2025/07/26-18:47:11.811669 754                Options.force_consistency_checks: 1
2025/07/26-18:47:11.811670 754                Options.report_bg_io_stats: 0
2025/07/26-18:47:11.811672 754                               Options.ttl: 2592000
2025/07/26-18:47:11.811674 754          Options.periodic_compaction_seconds: 0
2025/07/26-18:47:11.811676 754                        Options.default_temperature: kUnknown
2025/07/26-18:47:11.811678 754  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:47:11.811679 754    Options.preserve_internal_time_seconds: 0
2025/07/26-18:47:11.811681 754                       Options.enable_blob_files: false
2025/07/26-18:47:11.811683 754                           Options.min_blob_size: 0
2025/07/26-18:47:11.811685 754                          Options.blob_file_size: 268435456
2025/07/26-18:47:11.811687 754                   Options.blob_compression_type: NoCompression
2025/07/26-18:47:11.811689 754          Options.enable_blob_garbage_collection: false
2025/07/26-18:47:11.811691 754      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:47:11.811693 754 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:47:11.811695 754          Options.blob_compaction_readahead_size: 0
2025/07/26-18:47:11.811696 754                Options.blob_file_starting_level: 0
2025/07/26-18:47:11.811698 754         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:47:11.811700 754            Options.memtable_max_range_deletions: 0
2025/07/26-18:47:11.811879 754 [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-18:47:11.814362 754 [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-18:47:11.814369 754               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:47:11.814372 754           Options.merge_operator: None
2025/07/26-18:47:11.814373 754        Options.compaction_filter: None
2025/07/26-18:47:11.814375 754        Options.compaction_filter_factory: None
2025/07/26-18:47:11.814377 754  Options.sst_partitioner_factory: None
2025/07/26-18:47:11.814379 754         Options.memtable_factory: SkipListFactory
2025/07/26-18:47:11.814380 754            Options.table_factory: BlockBasedTable
2025/07/26-18:47:11.814397 754            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000182A3429BC0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000182A3452550
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:47:11.814399 754        Options.write_buffer_size: 67108864
2025/07/26-18:47:11.814401 754  Options.max_write_buffer_number: 2
2025/07/26-18:47:11.814403 754          Options.compression: Snappy
2025/07/26-18:47:11.814405 754                  Options.bottommost_compression: Disabled
2025/07/26-18:47:11.814407 754       Options.prefix_extractor: nullptr
2025/07/26-18:47:11.814408 754   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:47:11.814410 754             Options.num_levels: 7
2025/07/26-18:47:11.814412 754        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:47:11.814413 754     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:47:11.814415 754     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:47:11.814417 754            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:47:11.814419 754                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:47:11.814420 754               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:47:11.814423 754         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:47:11.814424 754         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:47:11.814426 754         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:47:11.814430 754                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:47:11.814434 754         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:47:11.814436 754         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:47:11.814437 754            Options.compression_opts.window_bits: -14
2025/07/26-18:47:11.814439 754                  Options.compression_opts.level: 32767
2025/07/26-18:47:11.814441 754               Options.compression_opts.strategy: 0
2025/07/26-18:47:11.814443 754         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:47:11.814444 754         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:47:11.814446 754         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:47:11.814448 754         Options.compression_opts.parallel_threads: 1
2025/07/26-18:47:11.814450 754                  Options.compression_opts.enabled: false
2025/07/26-18:47:11.814451 754         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:47:11.814453 754      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:47:11.814455 754          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:47:11.814457 754              Options.level0_stop_writes_trigger: 36
2025/07/26-18:47:11.814458 754                   Options.target_file_size_base: 67108864
2025/07/26-18:47:11.814460 754             Options.target_file_size_multiplier: 1
2025/07/26-18:47:11.814462 754                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:47:11.814464 754 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:47:11.814465 754          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:47:11.814468 754 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:47:11.814469 754 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:47:11.814471 754 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:47:11.814473 754 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:47:11.814475 754 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:47:11.814476 754 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:47:11.814478 754 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:47:11.814480 754       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:47:11.814482 754                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:47:11.814484 754   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:47:11.814485 754                        Options.arena_block_size: 1048576
2025/07/26-18:47:11.814487 754   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:47:11.814489 754   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:47:11.814491 754                Options.disable_auto_compactions: 0
2025/07/26-18:47:11.814493 754                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:47:11.814495 754                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:47:11.814497 754 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:47:11.814498 754 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:47:11.814500 754 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:47:11.814502 754 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:47:11.814504 754 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:47:11.814506 754 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:47:11.814507 754 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:47:11.814509 754 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:47:11.814512 754                   Options.table_properties_collectors: 
2025/07/26-18:47:11.814514 754                   Options.inplace_update_support: 0
2025/07/26-18:47:11.814517 754                 Options.inplace_update_num_locks: 10000
2025/07/26-18:47:11.814519 754               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:47:11.814521 754               Options.memtable_whole_key_filtering: 0
2025/07/26-18:47:11.814522 754   Options.memtable_huge_page_size: 0
2025/07/26-18:47:11.814524 754                           Options.bloom_locality: 0
2025/07/26-18:47:11.814526 754                    Options.max_successive_merges: 0
2025/07/26-18:47:11.814527 754                Options.optimize_filters_for_hits: 0
2025/07/26-18:47:11.814529 754                Options.paranoid_file_checks: 0
2025/07/26-18:47:11.814531 754                Options.force_consistency_checks: 1
2025/07/26-18:47:11.814533 754                Options.report_bg_io_stats: 0
2025/07/26-18:47:11.814534 754                               Options.ttl: 2592000
2025/07/26-18:47:11.814536 754          Options.periodic_compaction_seconds: 0
2025/07/26-18:47:11.814538 754                        Options.default_temperature: kUnknown
2025/07/26-18:47:11.814540 754  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:47:11.814542 754    Options.preserve_internal_time_seconds: 0
2025/07/26-18:47:11.814543 754                       Options.enable_blob_files: false
2025/07/26-18:47:11.814545 754                           Options.min_blob_size: 0
2025/07/26-18:47:11.814547 754                          Options.blob_file_size: 268435456
2025/07/26-18:47:11.814549 754                   Options.blob_compression_type: NoCompression
2025/07/26-18:47:11.814551 754          Options.enable_blob_garbage_collection: false
2025/07/26-18:47:11.814553 754      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:47:11.814555 754 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:47:11.814557 754          Options.blob_compaction_readahead_size: 0
2025/07/26-18:47:11.814559 754                Options.blob_file_starting_level: 0
2025/07/26-18:47:11.814561 754         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:47:11.814563 754            Options.memtable_max_range_deletions: 0
2025/07/26-18:47:11.814711 754 [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-18:47:11.817444 754 [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-18:47:11.817451 754               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:47:11.817453 754           Options.merge_operator: None
2025/07/26-18:47:11.817454 754        Options.compaction_filter: None
2025/07/26-18:47:11.817456 754        Options.compaction_filter_factory: None
2025/07/26-18:47:11.817458 754  Options.sst_partitioner_factory: None
2025/07/26-18:47:11.817459 754         Options.memtable_factory: SkipListFactory
2025/07/26-18:47:11.817461 754            Options.table_factory: BlockBasedTable
2025/07/26-18:47:11.817477 754            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000182A3429A40)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000182A3452280
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:47:11.817481 754        Options.write_buffer_size: 67108864
2025/07/26-18:47:11.817484 754  Options.max_write_buffer_number: 2
2025/07/26-18:47:11.817486 754          Options.compression: Snappy
2025/07/26-18:47:11.817487 754                  Options.bottommost_compression: Disabled
2025/07/26-18:47:11.817489 754       Options.prefix_extractor: nullptr
2025/07/26-18:47:11.817491 754   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:47:11.817493 754             Options.num_levels: 7
2025/07/26-18:47:11.817494 754        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:47:11.817496 754     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:47:11.817498 754     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:47:11.817499 754            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:47:11.817501 754                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:47:11.817503 754               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:47:11.817505 754         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:47:11.817506 754         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:47:11.817508 754         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:47:11.817510 754                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:47:11.817512 754         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:47:11.817514 754         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:47:11.817515 754            Options.compression_opts.window_bits: -14
2025/07/26-18:47:11.817517 754                  Options.compression_opts.level: 32767
2025/07/26-18:47:11.817519 754               Options.compression_opts.strategy: 0
2025/07/26-18:47:11.817520 754         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:47:11.817522 754         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:47:11.817524 754         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:47:11.817526 754         Options.compression_opts.parallel_threads: 1
2025/07/26-18:47:11.817527 754                  Options.compression_opts.enabled: false
2025/07/26-18:47:11.817529 754         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:47:11.817531 754      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:47:11.817533 754          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:47:11.817534 754              Options.level0_stop_writes_trigger: 36
2025/07/26-18:47:11.817536 754                   Options.target_file_size_base: 67108864
2025/07/26-18:47:11.817538 754             Options.target_file_size_multiplier: 1
2025/07/26-18:47:11.817540 754                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:47:11.817541 754 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:47:11.817543 754          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:47:11.817545 754 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:47:11.817547 754 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:47:11.817549 754 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:47:11.817550 754 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:47:11.817552 754 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:47:11.817554 754 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:47:11.817555 754 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:47:11.817557 754       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:47:11.817559 754                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:47:11.817561 754   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:47:11.817562 754                        Options.arena_block_size: 1048576
2025/07/26-18:47:11.817565 754   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:47:11.817567 754   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:47:11.817569 754                Options.disable_auto_compactions: 0
2025/07/26-18:47:11.817571 754                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:47:11.817572 754                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:47:11.817574 754 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:47:11.817576 754 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:47:11.817578 754 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:47:11.817579 754 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:47:11.817581 754 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:47:11.817583 754 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:47:11.817585 754 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:47:11.817587 754 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:47:11.817589 754                   Options.table_properties_collectors: 
2025/07/26-18:47:11.817591 754                   Options.inplace_update_support: 0
2025/07/26-18:47:11.817593 754                 Options.inplace_update_num_locks: 10000
2025/07/26-18:47:11.817595 754               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:47:11.817597 754               Options.memtable_whole_key_filtering: 0
2025/07/26-18:47:11.817598 754   Options.memtable_huge_page_size: 0
2025/07/26-18:47:11.817600 754                           Options.bloom_locality: 0
2025/07/26-18:47:11.817602 754                    Options.max_successive_merges: 0
2025/07/26-18:47:11.817603 754                Options.optimize_filters_for_hits: 0
2025/07/26-18:47:11.817605 754                Options.paranoid_file_checks: 0
2025/07/26-18:47:11.817607 754                Options.force_consistency_checks: 1
2025/07/26-18:47:11.817609 754                Options.report_bg_io_stats: 0
2025/07/26-18:47:11.817614 754                               Options.ttl: 2592000
2025/07/26-18:47:11.817616 754          Options.periodic_compaction_seconds: 0
2025/07/26-18:47:11.817618 754                        Options.default_temperature: kUnknown
2025/07/26-18:47:11.817619 754  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:47:11.817621 754    Options.preserve_internal_time_seconds: 0
2025/07/26-18:47:11.817623 754                       Options.enable_blob_files: false
2025/07/26-18:47:11.817624 754                           Options.min_blob_size: 0
2025/07/26-18:47:11.817626 754                          Options.blob_file_size: 268435456
2025/07/26-18:47:11.817628 754                   Options.blob_compression_type: NoCompression
2025/07/26-18:47:11.817630 754          Options.enable_blob_garbage_collection: false
2025/07/26-18:47:11.817632 754      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:47:11.817634 754 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:47:11.817636 754          Options.blob_compaction_readahead_size: 0
2025/07/26-18:47:11.817637 754                Options.blob_file_starting_level: 0
2025/07/26-18:47:11.817639 754         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:47:11.817641 754            Options.memtable_max_range_deletions: 0
2025/07/26-18:47:11.817782 754 [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-18:47:11.824446 754 [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 00000182A4F068B0
2025/07/26-18:47:11.824688 754 DB pointer 00000182A34A6440
2025/07/26-18:47:11.825234 4b90 [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-18:47:11.825241 4b90 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000182A3452A00#22976 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 4.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000182A3452AF0#22976 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000182A3452820#22976 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000182A3452550#22976 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000182A3452280#22976 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
