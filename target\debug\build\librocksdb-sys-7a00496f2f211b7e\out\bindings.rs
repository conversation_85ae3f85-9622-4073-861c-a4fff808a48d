/* automatically generated by rust-bindgen 0.69.5 */

pub const __bool_true_false_are_defined: u32 = 1;
pub const true_: u32 = 1;
pub const false_: u32 = 0;
pub const __MINGW64_VERSION_MAJOR: u32 = 13;
pub const __MINGW64_VERSION_MINOR: u32 = 0;
pub const __MINGW64_VERSION_BUGFIX: u32 = 0;
pub const __MINGW64_VERSION_RC: u32 = 0;
pub const __MINGW64_VERSION_STATE: &[u8; 6] = b"alpha\0";
pub const __MINGW32_MAJOR_VERSION: u32 = 3;
pub const __MINGW32_MINOR_VERSION: u32 = 11;
pub const _M_AMD64: u32 = 100;
pub const _M_X64: u32 = 100;
pub const __: u32 = 1;
pub const __MINGW_USE_UNDERSCORE_PREFIX: u32 = 0;
pub const __MINGW_HAVE_ANSI_C99_PRINTF: u32 = 1;
pub const __MINGW_HAVE_WIDE_C99_PRINTF: u32 = 1;
pub const __MINGW_HAVE_ANSI_C99_SCANF: u32 = 1;
pub const __MINGW_HAVE_WIDE_C99_SCANF: u32 = 1;
pub const __MINGW_SEC_WARN_STR : & [u8 ; 92] = b"This function or variable may be unsafe, use _CRT_SECURE_NO_WARNINGS to disable deprecation\0" ;
pub const __MINGW_MSVC2005_DEPREC_STR : & [u8 ; 117] = b"This POSIX function is deprecated beginning in Visual C++ 2005, use _CRT_NONSTDC_NO_DEPRECATE to disable deprecation\0" ;
pub const __MINGW_FORTIFY_LEVEL: u32 = 0;
pub const __MINGW_FORTIFY_VA_ARG: u32 = 0;
pub const _CRT_SECURE_CPP_OVERLOAD_SECURE_NAMES: u32 = 0;
pub const _CRT_SECURE_CPP_OVERLOAD_SECURE_NAMES_MEMORY: u32 = 0;
pub const _CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES: u32 = 0;
pub const _CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT: u32 = 0;
pub const _CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_MEMORY: u32 = 0;
pub const __USE_CRTIMP: u32 = 1;
pub const USE___UUIDOF: u32 = 0;
pub const __CRT__NO_INLINE: u32 = 1;
pub const __MSVCRT_VERSION__: u32 = 3584;
pub const _WIN32_WINNT: u32 = 1539;
pub const MINGW_HAS_SECURE_API: u32 = 1;
pub const __STDC_SECURE_LIB__: u32 = 200411;
pub const __GOT_SECURE_LIB__: u32 = 200411;
pub const MINGW_HAS_DDK_H: u32 = 1;
pub const _CRT_PACKING: u32 = 8;
pub const _SECURECRT_FILL_BUFFER_PATTERN: u32 = 253;
pub const _ARGMAX: u32 = 100;
pub const __USE_MINGW_ANSI_STDIO: u32 = 0;
pub const INT8_MIN: i32 = -128;
pub const INT16_MIN: i32 = -32768;
pub const INT32_MIN: i32 = -2147483648;
pub const INT64_MIN: i64 = -9223372036854775808;
pub const INT8_MAX: u32 = 127;
pub const INT16_MAX: u32 = 32767;
pub const INT32_MAX: u32 = 2147483647;
pub const INT64_MAX: u64 = 9223372036854775807;
pub const UINT8_MAX: u32 = 255;
pub const UINT16_MAX: u32 = 65535;
pub const UINT32_MAX: u32 = 4294967295;
pub const UINT64_MAX: i32 = -1;
pub const INT_LEAST8_MIN: i32 = -128;
pub const INT_LEAST16_MIN: i32 = -32768;
pub const INT_LEAST32_MIN: i32 = -2147483648;
pub const INT_LEAST64_MIN: i64 = -9223372036854775808;
pub const INT_LEAST8_MAX: u32 = 127;
pub const INT_LEAST16_MAX: u32 = 32767;
pub const INT_LEAST32_MAX: u32 = 2147483647;
pub const INT_LEAST64_MAX: u64 = 9223372036854775807;
pub const UINT_LEAST8_MAX: u32 = 255;
pub const UINT_LEAST16_MAX: u32 = 65535;
pub const UINT_LEAST32_MAX: u32 = 4294967295;
pub const UINT_LEAST64_MAX: i32 = -1;
pub const INT_FAST8_MIN: i32 = -128;
pub const INT_FAST16_MIN: i32 = -32768;
pub const INT_FAST32_MIN: i32 = -2147483648;
pub const INT_FAST64_MIN: i64 = -9223372036854775808;
pub const INT_FAST8_MAX: u32 = 127;
pub const INT_FAST16_MAX: u32 = 32767;
pub const INT_FAST32_MAX: u32 = 2147483647;
pub const INT_FAST64_MAX: u64 = 9223372036854775807;
pub const UINT_FAST8_MAX: u32 = 255;
pub const UINT_FAST16_MAX: u32 = 65535;
pub const UINT_FAST32_MAX: u32 = 4294967295;
pub const UINT_FAST64_MAX: i32 = -1;
pub const INTPTR_MIN: i64 = -9223372036854775808;
pub const INTPTR_MAX: u64 = 9223372036854775807;
pub const UINTPTR_MAX: i32 = -1;
pub const INTMAX_MIN: i64 = -9223372036854775808;
pub const INTMAX_MAX: u64 = 9223372036854775807;
pub const UINTMAX_MAX: i32 = -1;
pub const PTRDIFF_MIN: i64 = -9223372036854775808;
pub const PTRDIFF_MAX: u64 = 9223372036854775807;
pub const SIG_ATOMIC_MIN: i32 = -2147483648;
pub const SIG_ATOMIC_MAX: u32 = 2147483647;
pub const SIZE_MAX: i32 = -1;
pub const WCHAR_MIN: u32 = 0;
pub const WCHAR_MAX: u32 = 65535;
pub const WINT_MIN: u32 = 0;
pub const WINT_MAX: u32 = 65535;
pub type wchar_t = libc::c_ushort;
pub type __gnuc_va_list = __builtin_va_list;
pub type va_list = __gnuc_va_list;
extern "C" {
    pub fn __mingw_get_crt_info() -> *const libc::c_char;
}
pub type rsize_t = usize;
pub type wint_t = libc::c_ushort;
pub type wctype_t = libc::c_ushort;
pub type errno_t = libc::c_int;
pub type __time32_t = libc::c_long;
pub type __time64_t = libc::c_longlong;
pub type time_t = __time64_t;
#[repr(C)]
#[derive(Copy, Clone)]
pub struct threadmbcinfostruct {
    _unused: [u8; 0],
}
pub type pthreadlocinfo = *mut threadlocaleinfostruct;
pub type pthreadmbcinfo = *mut threadmbcinfostruct;
#[repr(C)]
#[derive(Copy, Clone)]
pub struct __lc_time_data {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct localeinfo_struct {
    pub locinfo: pthreadlocinfo,
    pub mbcinfo: pthreadmbcinfo,
}
#[test]
fn bindgen_test_layout_localeinfo_struct() {
    const UNINIT: ::std::mem::MaybeUninit<localeinfo_struct> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<localeinfo_struct>(),
        16usize,
        concat!("Size of: ", stringify!(localeinfo_struct))
    );
    assert_eq!(
        ::std::mem::align_of::<localeinfo_struct>(),
        8usize,
        concat!("Alignment of ", stringify!(localeinfo_struct))
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).locinfo) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(localeinfo_struct),
            "::",
            stringify!(locinfo)
        )
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).mbcinfo) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(localeinfo_struct),
            "::",
            stringify!(mbcinfo)
        )
    );
}
pub type _locale_tstruct = localeinfo_struct;
pub type _locale_t = *mut localeinfo_struct;
#[repr(C)]
#[derive(Copy, Clone)]
pub struct tagLC_ID {
    pub wLanguage: libc::c_ushort,
    pub wCountry: libc::c_ushort,
    pub wCodePage: libc::c_ushort,
}
#[test]
fn bindgen_test_layout_tagLC_ID() {
    const UNINIT: ::std::mem::MaybeUninit<tagLC_ID> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<tagLC_ID>(),
        6usize,
        concat!("Size of: ", stringify!(tagLC_ID))
    );
    assert_eq!(
        ::std::mem::align_of::<tagLC_ID>(),
        2usize,
        concat!("Alignment of ", stringify!(tagLC_ID))
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).wLanguage) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(tagLC_ID),
            "::",
            stringify!(wLanguage)
        )
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).wCountry) as usize - ptr as usize },
        2usize,
        concat!(
            "Offset of field: ",
            stringify!(tagLC_ID),
            "::",
            stringify!(wCountry)
        )
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).wCodePage) as usize - ptr as usize },
        4usize,
        concat!(
            "Offset of field: ",
            stringify!(tagLC_ID),
            "::",
            stringify!(wCodePage)
        )
    );
}
pub type LC_ID = tagLC_ID;
pub type LPLC_ID = *mut tagLC_ID;
#[repr(C)]
#[derive(Copy, Clone)]
pub struct threadlocaleinfostruct {
    pub _locale_pctype: *const libc::c_ushort,
    pub _locale_mb_cur_max: libc::c_int,
    pub _locale_lc_codepage: libc::c_uint,
}
#[test]
fn bindgen_test_layout_threadlocaleinfostruct() {
    const UNINIT: ::std::mem::MaybeUninit<threadlocaleinfostruct> =
        ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<threadlocaleinfostruct>(),
        16usize,
        concat!("Size of: ", stringify!(threadlocaleinfostruct))
    );
    assert_eq!(
        ::std::mem::align_of::<threadlocaleinfostruct>(),
        8usize,
        concat!("Alignment of ", stringify!(threadlocaleinfostruct))
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr)._locale_pctype) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(threadlocaleinfostruct),
            "::",
            stringify!(_locale_pctype)
        )
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr)._locale_mb_cur_max) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(threadlocaleinfostruct),
            "::",
            stringify!(_locale_mb_cur_max)
        )
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr)._locale_lc_codepage) as usize - ptr as usize },
        12usize,
        concat!(
            "Offset of field: ",
            stringify!(threadlocaleinfostruct),
            "::",
            stringify!(_locale_lc_codepage)
        )
    );
}
pub type threadlocinfo = threadlocaleinfostruct;
pub type int_least8_t = libc::c_schar;
pub type uint_least8_t = libc::c_uchar;
pub type int_least16_t = libc::c_short;
pub type uint_least16_t = libc::c_ushort;
pub type int_least32_t = libc::c_int;
pub type uint_least32_t = libc::c_uint;
pub type int_least64_t = libc::c_longlong;
pub type uint_least64_t = libc::c_ulonglong;
pub type int_fast8_t = libc::c_schar;
pub type uint_fast8_t = libc::c_uchar;
pub type int_fast16_t = libc::c_short;
pub type uint_fast16_t = libc::c_ushort;
pub type int_fast32_t = libc::c_int;
pub type uint_fast32_t = libc::c_uint;
pub type int_fast64_t = libc::c_longlong;
pub type uint_fast64_t = libc::c_ulonglong;
pub type intmax_t = libc::c_longlong;
pub type uintmax_t = libc::c_ulonglong;
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_backup_engine_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_backup_engine_info_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_backup_engine_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_restore_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_memory_allocator_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_lru_cache_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_hyper_clock_cache_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_cache_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_write_buffer_manager_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_compactionfilter_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_compactionfiltercontext_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_compactionfilterfactory_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_comparator_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_dbpath_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_env_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_fifo_compaction_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_filelock_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_filterpolicy_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_flushoptions_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_iterator_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_logger_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_mergeoperator_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_compactoptions_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_block_based_table_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_cuckoo_table_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_randomfile_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_readoptions_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_seqfile_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_slicetransform_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_snapshot_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_writablefile_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_writebatch_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_writebatch_wi_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_writeoptions_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_universal_compaction_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_livefiles_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_column_family_handle_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_column_family_metadata_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_level_metadata_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_sst_file_metadata_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_envoptions_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_ingestexternalfileoptions_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_sstfilewriter_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_ratelimiter_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_perfcontext_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_pinnableslice_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_transactiondb_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_transactiondb_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_transaction_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_optimistictransactiondb_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_optimistictransaction_options_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_transaction_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_checkpoint_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_wal_iterator_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_wal_readoptions_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_memory_consumers_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_memory_usage_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_statistics_histogram_data_t {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rocksdb_wait_for_compact_options_t {
    _unused: [u8; 0],
}
extern "C" {
    pub fn rocksdb_open(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_open_with_ttl(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        ttl: libc::c_int,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_open_for_read_only(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        error_if_wal_file_exists: libc::c_uchar,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_open_as_secondary(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        secondary_path: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_backup_engine_open(
        options: *const rocksdb_options_t,
        path: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_backup_engine_t;
}
extern "C" {
    pub fn rocksdb_backup_engine_open_opts(
        options: *const rocksdb_backup_engine_options_t,
        env: *mut rocksdb_env_t,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_backup_engine_t;
}
extern "C" {
    pub fn rocksdb_backup_engine_create_new_backup(
        be: *mut rocksdb_backup_engine_t,
        db: *mut rocksdb_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_create_new_backup_flush(
        be: *mut rocksdb_backup_engine_t,
        db: *mut rocksdb_t,
        flush_before_backup: libc::c_uchar,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_purge_old_backups(
        be: *mut rocksdb_backup_engine_t,
        num_backups_to_keep: u32,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_restore_options_create() -> *mut rocksdb_restore_options_t;
}
extern "C" {
    pub fn rocksdb_restore_options_destroy(opt: *mut rocksdb_restore_options_t);
}
extern "C" {
    pub fn rocksdb_restore_options_set_keep_log_files(
        opt: *mut rocksdb_restore_options_t,
        v: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_verify_backup(
        be: *mut rocksdb_backup_engine_t,
        backup_id: u32,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_restore_db_from_latest_backup(
        be: *mut rocksdb_backup_engine_t,
        db_dir: *const libc::c_char,
        wal_dir: *const libc::c_char,
        restore_options: *const rocksdb_restore_options_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_restore_db_from_backup(
        be: *mut rocksdb_backup_engine_t,
        db_dir: *const libc::c_char,
        wal_dir: *const libc::c_char,
        restore_options: *const rocksdb_restore_options_t,
        backup_id: u32,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_get_backup_info(
        be: *mut rocksdb_backup_engine_t,
    ) -> *const rocksdb_backup_engine_info_t;
}
extern "C" {
    pub fn rocksdb_backup_engine_info_count(
        info: *const rocksdb_backup_engine_info_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_backup_engine_info_timestamp(
        info: *const rocksdb_backup_engine_info_t,
        index: libc::c_int,
    ) -> i64;
}
extern "C" {
    pub fn rocksdb_backup_engine_info_backup_id(
        info: *const rocksdb_backup_engine_info_t,
        index: libc::c_int,
    ) -> u32;
}
extern "C" {
    pub fn rocksdb_backup_engine_info_size(
        info: *const rocksdb_backup_engine_info_t,
        index: libc::c_int,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_backup_engine_info_number_files(
        info: *const rocksdb_backup_engine_info_t,
        index: libc::c_int,
    ) -> u32;
}
extern "C" {
    pub fn rocksdb_backup_engine_info_destroy(info: *const rocksdb_backup_engine_info_t);
}
extern "C" {
    pub fn rocksdb_backup_engine_close(be: *mut rocksdb_backup_engine_t);
}
extern "C" {
    pub fn rocksdb_put_with_ts(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        ts: *const libc::c_char,
        tslen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_put_cf_with_ts(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        ts: *const libc::c_char,
        tslen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_delete_with_ts(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        ts: *const libc::c_char,
        tslen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_delete_cf_with_ts(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        ts: *const libc::c_char,
        tslen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_singledelete(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_singledelete_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_singledelete_with_ts(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        ts: *const libc::c_char,
        tslen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_singledelete_cf_with_ts(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        ts: *const libc::c_char,
        tslen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_increase_full_history_ts_low(
        db: *mut rocksdb_t,
        column_family: *mut rocksdb_column_family_handle_t,
        ts_low: *const libc::c_char,
        ts_lowlen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_get_full_history_ts_low(
        db: *mut rocksdb_t,
        column_family: *mut rocksdb_column_family_handle_t,
        ts_lowlen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_create(
        backup_dir: *const libc::c_char,
    ) -> *mut rocksdb_backup_engine_options_t;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_backup_dir(
        options: *mut rocksdb_backup_engine_options_t,
        backup_dir: *const libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_env(
        options: *mut rocksdb_backup_engine_options_t,
        env: *mut rocksdb_env_t,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_share_table_files(
        options: *mut rocksdb_backup_engine_options_t,
        val: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_get_share_table_files(
        options: *mut rocksdb_backup_engine_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_sync(
        options: *mut rocksdb_backup_engine_options_t,
        val: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_get_sync(
        options: *mut rocksdb_backup_engine_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_destroy_old_data(
        options: *mut rocksdb_backup_engine_options_t,
        val: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_get_destroy_old_data(
        options: *mut rocksdb_backup_engine_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_backup_log_files(
        options: *mut rocksdb_backup_engine_options_t,
        val: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_get_backup_log_files(
        options: *mut rocksdb_backup_engine_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_backup_rate_limit(
        options: *mut rocksdb_backup_engine_options_t,
        limit: u64,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_get_backup_rate_limit(
        options: *mut rocksdb_backup_engine_options_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_restore_rate_limit(
        options: *mut rocksdb_backup_engine_options_t,
        limit: u64,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_get_restore_rate_limit(
        options: *mut rocksdb_backup_engine_options_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_max_background_operations(
        options: *mut rocksdb_backup_engine_options_t,
        val: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_get_max_background_operations(
        options: *mut rocksdb_backup_engine_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_callback_trigger_interval_size(
        options: *mut rocksdb_backup_engine_options_t,
        size: u64,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_get_callback_trigger_interval_size(
        options: *mut rocksdb_backup_engine_options_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_max_valid_backups_to_open(
        options: *mut rocksdb_backup_engine_options_t,
        val: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_get_max_valid_backups_to_open(
        options: *mut rocksdb_backup_engine_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_set_share_files_with_checksum_naming(
        options: *mut rocksdb_backup_engine_options_t,
        val: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_backup_engine_options_get_share_files_with_checksum_naming(
        options: *mut rocksdb_backup_engine_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_backup_engine_options_destroy(arg1: *mut rocksdb_backup_engine_options_t);
}
extern "C" {
    pub fn rocksdb_checkpoint_object_create(
        db: *mut rocksdb_t,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_checkpoint_t;
}
extern "C" {
    pub fn rocksdb_checkpoint_create(
        checkpoint: *mut rocksdb_checkpoint_t,
        checkpoint_dir: *const libc::c_char,
        log_size_for_flush: u64,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_checkpoint_object_destroy(checkpoint: *mut rocksdb_checkpoint_t);
}
extern "C" {
    pub fn rocksdb_open_and_trim_history(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        num_column_families: libc::c_int,
        column_family_names: *const *const libc::c_char,
        column_family_options: *const *const rocksdb_options_t,
        column_family_handles: *mut *mut rocksdb_column_family_handle_t,
        trim_ts: *mut libc::c_char,
        trim_tslen: usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_open_column_families(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        num_column_families: libc::c_int,
        column_family_names: *const *const libc::c_char,
        column_family_options: *const *const rocksdb_options_t,
        column_family_handles: *mut *mut rocksdb_column_family_handle_t,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_open_column_families_with_ttl(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        num_column_families: libc::c_int,
        column_family_names: *const *const libc::c_char,
        column_family_options: *const *const rocksdb_options_t,
        column_family_handles: *mut *mut rocksdb_column_family_handle_t,
        ttls: *const libc::c_int,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_open_for_read_only_column_families(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        num_column_families: libc::c_int,
        column_family_names: *const *const libc::c_char,
        column_family_options: *const *const rocksdb_options_t,
        column_family_handles: *mut *mut rocksdb_column_family_handle_t,
        error_if_wal_file_exists: libc::c_uchar,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_open_as_secondary_column_families(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        secondary_path: *const libc::c_char,
        num_column_families: libc::c_int,
        column_family_names: *const *const libc::c_char,
        column_family_options: *const *const rocksdb_options_t,
        column_family_handles: *mut *mut rocksdb_column_family_handle_t,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_list_column_families(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        lencf: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_list_column_families_destroy(list: *mut *mut libc::c_char, len: usize);
}
extern "C" {
    pub fn rocksdb_create_column_family(
        db: *mut rocksdb_t,
        column_family_options: *const rocksdb_options_t,
        column_family_name: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_column_family_handle_t;
}
extern "C" {
    pub fn rocksdb_create_column_families(
        db: *mut rocksdb_t,
        column_family_options: *const rocksdb_options_t,
        num_column_families: libc::c_int,
        column_family_names: *const *const libc::c_char,
        lencfs: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut *mut rocksdb_column_family_handle_t;
}
extern "C" {
    pub fn rocksdb_create_column_families_destroy(list: *mut *mut rocksdb_column_family_handle_t);
}
extern "C" {
    pub fn rocksdb_create_column_family_with_ttl(
        db: *mut rocksdb_t,
        column_family_options: *const rocksdb_options_t,
        column_family_name: *const libc::c_char,
        ttl: libc::c_int,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_column_family_handle_t;
}
extern "C" {
    pub fn rocksdb_drop_column_family(
        db: *mut rocksdb_t,
        handle: *mut rocksdb_column_family_handle_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_column_family_handle_destroy(arg1: *mut rocksdb_column_family_handle_t);
}
extern "C" {
    pub fn rocksdb_column_family_handle_get_id(handle: *mut rocksdb_column_family_handle_t) -> u32;
}
extern "C" {
    pub fn rocksdb_column_family_handle_get_name(
        handle: *mut rocksdb_column_family_handle_t,
        name_len: *mut usize,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_close(db: *mut rocksdb_t);
}
extern "C" {
    pub fn rocksdb_put(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_put_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_delete(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_delete_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_delete_range_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        end_key: *const libc::c_char,
        end_key_len: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_merge(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_merge_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_write(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        batch: *mut rocksdb_writebatch_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_get(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        vallen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_get_with_ts(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        vallen: *mut usize,
        ts: *mut *mut libc::c_char,
        tslen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_get_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        vallen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_get_cf_with_ts(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        vallen: *mut usize,
        ts: *mut *mut libc::c_char,
        tslen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_multi_get(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values_list: *mut *mut libc::c_char,
        values_list_sizes: *mut usize,
        errs: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_multi_get_with_ts(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values_list: *mut *mut libc::c_char,
        values_list_sizes: *mut usize,
        timestamp_list: *mut *mut libc::c_char,
        timestamp_list_sizes: *mut usize,
        errs: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_multi_get_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        column_families: *const *const rocksdb_column_family_handle_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values_list: *mut *mut libc::c_char,
        values_list_sizes: *mut usize,
        errs: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_multi_get_cf_with_ts(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        column_families: *const *const rocksdb_column_family_handle_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values_list: *mut *mut libc::c_char,
        values_list_sizes: *mut usize,
        timestamps_list: *mut *mut libc::c_char,
        timestamps_list_sizes: *mut usize,
        errs: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_batched_multi_get_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values: *mut *mut rocksdb_pinnableslice_t,
        errs: *mut *mut libc::c_char,
        sorted_input: bool,
    );
}
extern "C" {
    pub fn rocksdb_key_may_exist(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        key_len: usize,
        value: *mut *mut libc::c_char,
        val_len: *mut usize,
        timestamp: *const libc::c_char,
        timestamp_len: usize,
        value_found: *mut libc::c_uchar,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_key_may_exist_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        key_len: usize,
        value: *mut *mut libc::c_char,
        val_len: *mut usize,
        timestamp: *const libc::c_char,
        timestamp_len: usize,
        value_found: *mut libc::c_uchar,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_create_iterator(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
    ) -> *mut rocksdb_iterator_t;
}
extern "C" {
    pub fn rocksdb_get_updates_since(
        db: *mut rocksdb_t,
        seq_number: u64,
        options: *const rocksdb_wal_readoptions_t,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_wal_iterator_t;
}
extern "C" {
    pub fn rocksdb_create_iterator_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
    ) -> *mut rocksdb_iterator_t;
}
extern "C" {
    pub fn rocksdb_create_iterators(
        db: *mut rocksdb_t,
        opts: *mut rocksdb_readoptions_t,
        column_families: *mut *mut rocksdb_column_family_handle_t,
        iterators: *mut *mut rocksdb_iterator_t,
        size: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_create_snapshot(db: *mut rocksdb_t) -> *const rocksdb_snapshot_t;
}
extern "C" {
    pub fn rocksdb_release_snapshot(db: *mut rocksdb_t, snapshot: *const rocksdb_snapshot_t);
}
extern "C" {
    pub fn rocksdb_property_value(
        db: *mut rocksdb_t,
        propname: *const libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_property_int(
        db: *mut rocksdb_t,
        propname: *const libc::c_char,
        out_val: *mut u64,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_property_int_cf(
        db: *mut rocksdb_t,
        column_family: *mut rocksdb_column_family_handle_t,
        propname: *const libc::c_char,
        out_val: *mut u64,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_property_value_cf(
        db: *mut rocksdb_t,
        column_family: *mut rocksdb_column_family_handle_t,
        propname: *const libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_approximate_sizes(
        db: *mut rocksdb_t,
        num_ranges: libc::c_int,
        range_start_key: *const *const libc::c_char,
        range_start_key_len: *const usize,
        range_limit_key: *const *const libc::c_char,
        range_limit_key_len: *const usize,
        sizes: *mut u64,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_approximate_sizes_cf(
        db: *mut rocksdb_t,
        column_family: *mut rocksdb_column_family_handle_t,
        num_ranges: libc::c_int,
        range_start_key: *const *const libc::c_char,
        range_start_key_len: *const usize,
        range_limit_key: *const *const libc::c_char,
        range_limit_key_len: *const usize,
        sizes: *mut u64,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_compact_range(
        db: *mut rocksdb_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        limit_key: *const libc::c_char,
        limit_key_len: usize,
    );
}
extern "C" {
    pub fn rocksdb_compact_range_cf(
        db: *mut rocksdb_t,
        column_family: *mut rocksdb_column_family_handle_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        limit_key: *const libc::c_char,
        limit_key_len: usize,
    );
}
extern "C" {
    pub fn rocksdb_suggest_compact_range(
        db: *mut rocksdb_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        limit_key: *const libc::c_char,
        limit_key_len: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_suggest_compact_range_cf(
        db: *mut rocksdb_t,
        column_family: *mut rocksdb_column_family_handle_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        limit_key: *const libc::c_char,
        limit_key_len: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_compact_range_opt(
        db: *mut rocksdb_t,
        opt: *mut rocksdb_compactoptions_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        limit_key: *const libc::c_char,
        limit_key_len: usize,
    );
}
extern "C" {
    pub fn rocksdb_compact_range_cf_opt(
        db: *mut rocksdb_t,
        column_family: *mut rocksdb_column_family_handle_t,
        opt: *mut rocksdb_compactoptions_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        limit_key: *const libc::c_char,
        limit_key_len: usize,
    );
}
extern "C" {
    pub fn rocksdb_delete_file(db: *mut rocksdb_t, name: *const libc::c_char);
}
extern "C" {
    pub fn rocksdb_livefiles(db: *mut rocksdb_t) -> *const rocksdb_livefiles_t;
}
extern "C" {
    pub fn rocksdb_flush(
        db: *mut rocksdb_t,
        options: *const rocksdb_flushoptions_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_flush_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_flushoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_flush_cfs(
        db: *mut rocksdb_t,
        options: *const rocksdb_flushoptions_t,
        column_family: *mut *mut rocksdb_column_family_handle_t,
        num_column_families: libc::c_int,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_flush_wal(
        db: *mut rocksdb_t,
        sync: libc::c_uchar,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_disable_file_deletions(db: *mut rocksdb_t, errptr: *mut *mut libc::c_char);
}
extern "C" {
    pub fn rocksdb_enable_file_deletions(
        db: *mut rocksdb_t,
        force: libc::c_uchar,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_destroy_db(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_repair_db(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_iter_destroy(arg1: *mut rocksdb_iterator_t);
}
extern "C" {
    pub fn rocksdb_iter_valid(arg1: *const rocksdb_iterator_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_iter_seek_to_first(arg1: *mut rocksdb_iterator_t);
}
extern "C" {
    pub fn rocksdb_iter_seek_to_last(arg1: *mut rocksdb_iterator_t);
}
extern "C" {
    pub fn rocksdb_iter_seek(arg1: *mut rocksdb_iterator_t, k: *const libc::c_char, klen: usize);
}
extern "C" {
    pub fn rocksdb_iter_seek_for_prev(
        arg1: *mut rocksdb_iterator_t,
        k: *const libc::c_char,
        klen: usize,
    );
}
extern "C" {
    pub fn rocksdb_iter_next(arg1: *mut rocksdb_iterator_t);
}
extern "C" {
    pub fn rocksdb_iter_prev(arg1: *mut rocksdb_iterator_t);
}
extern "C" {
    pub fn rocksdb_iter_key(
        arg1: *const rocksdb_iterator_t,
        klen: *mut usize,
    ) -> *const libc::c_char;
}
extern "C" {
    pub fn rocksdb_iter_value(
        arg1: *const rocksdb_iterator_t,
        vlen: *mut usize,
    ) -> *const libc::c_char;
}
extern "C" {
    pub fn rocksdb_iter_timestamp(
        arg1: *const rocksdb_iterator_t,
        tslen: *mut usize,
    ) -> *const libc::c_char;
}
extern "C" {
    pub fn rocksdb_iter_get_error(arg1: *const rocksdb_iterator_t, errptr: *mut *mut libc::c_char);
}
extern "C" {
    pub fn rocksdb_wal_iter_next(iter: *mut rocksdb_wal_iterator_t);
}
extern "C" {
    pub fn rocksdb_wal_iter_valid(arg1: *const rocksdb_wal_iterator_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_wal_iter_status(
        iter: *const rocksdb_wal_iterator_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_wal_iter_get_batch(
        iter: *const rocksdb_wal_iterator_t,
        seq: *mut u64,
    ) -> *mut rocksdb_writebatch_t;
}
extern "C" {
    pub fn rocksdb_get_latest_sequence_number(db: *mut rocksdb_t) -> u64;
}
extern "C" {
    pub fn rocksdb_wal_iter_destroy(iter: *const rocksdb_wal_iterator_t);
}
extern "C" {
    pub fn rocksdb_writebatch_create() -> *mut rocksdb_writebatch_t;
}
extern "C" {
    pub fn rocksdb_writebatch_create_from(
        rep: *const libc::c_char,
        size: usize,
    ) -> *mut rocksdb_writebatch_t;
}
extern "C" {
    pub fn rocksdb_writebatch_destroy(arg1: *mut rocksdb_writebatch_t);
}
extern "C" {
    pub fn rocksdb_writebatch_clear(arg1: *mut rocksdb_writebatch_t);
}
extern "C" {
    pub fn rocksdb_writebatch_count(arg1: *mut rocksdb_writebatch_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_writebatch_put(
        arg1: *mut rocksdb_writebatch_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_put_cf(
        arg1: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_put_cf_with_ts(
        arg1: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        ts: *const libc::c_char,
        tslen: usize,
        val: *const libc::c_char,
        vlen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_putv(
        b: *mut rocksdb_writebatch_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        num_values: libc::c_int,
        values_list: *const *const libc::c_char,
        values_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_putv_cf(
        b: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        num_values: libc::c_int,
        values_list: *const *const libc::c_char,
        values_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_merge(
        arg1: *mut rocksdb_writebatch_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_merge_cf(
        arg1: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_mergev(
        b: *mut rocksdb_writebatch_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        num_values: libc::c_int,
        values_list: *const *const libc::c_char,
        values_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_mergev_cf(
        b: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        num_values: libc::c_int,
        values_list: *const *const libc::c_char,
        values_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_delete(
        arg1: *mut rocksdb_writebatch_t,
        key: *const libc::c_char,
        klen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_singledelete(
        b: *mut rocksdb_writebatch_t,
        key: *const libc::c_char,
        klen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_delete_cf(
        arg1: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_delete_cf_with_ts(
        arg1: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        ts: *const libc::c_char,
        tslen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_singledelete_cf(
        b: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_singledelete_cf_with_ts(
        b: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        ts: *const libc::c_char,
        tslen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_deletev(
        b: *mut rocksdb_writebatch_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_deletev_cf(
        b: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_delete_range(
        b: *mut rocksdb_writebatch_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        end_key: *const libc::c_char,
        end_key_len: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_delete_range_cf(
        b: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        end_key: *const libc::c_char,
        end_key_len: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_delete_rangev(
        b: *mut rocksdb_writebatch_t,
        num_keys: libc::c_int,
        start_keys_list: *const *const libc::c_char,
        start_keys_list_sizes: *const usize,
        end_keys_list: *const *const libc::c_char,
        end_keys_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_delete_rangev_cf(
        b: *mut rocksdb_writebatch_t,
        column_family: *mut rocksdb_column_family_handle_t,
        num_keys: libc::c_int,
        start_keys_list: *const *const libc::c_char,
        start_keys_list_sizes: *const usize,
        end_keys_list: *const *const libc::c_char,
        end_keys_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_put_log_data(
        arg1: *mut rocksdb_writebatch_t,
        blob: *const libc::c_char,
        len: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_iterate(
        arg1: *mut rocksdb_writebatch_t,
        state: *mut libc::c_void,
        put: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                k: *const libc::c_char,
                klen: usize,
                v: *const libc::c_char,
                vlen: usize,
            ),
        >,
        deleted: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut libc::c_void, k: *const libc::c_char, klen: usize),
        >,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_data(
        arg1: *mut rocksdb_writebatch_t,
        size: *mut usize,
    ) -> *const libc::c_char;
}
extern "C" {
    pub fn rocksdb_writebatch_set_save_point(arg1: *mut rocksdb_writebatch_t);
}
extern "C" {
    pub fn rocksdb_writebatch_rollback_to_save_point(
        arg1: *mut rocksdb_writebatch_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_pop_save_point(
        arg1: *mut rocksdb_writebatch_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_create(
        reserved_bytes: usize,
        overwrite_keys: libc::c_uchar,
    ) -> *mut rocksdb_writebatch_wi_t;
}
extern "C" {
    pub fn rocksdb_writebatch_wi_create_from(
        rep: *const libc::c_char,
        size: usize,
    ) -> *mut rocksdb_writebatch_wi_t;
}
extern "C" {
    pub fn rocksdb_writebatch_wi_destroy(arg1: *mut rocksdb_writebatch_wi_t);
}
extern "C" {
    pub fn rocksdb_writebatch_wi_clear(arg1: *mut rocksdb_writebatch_wi_t);
}
extern "C" {
    pub fn rocksdb_writebatch_wi_count(b: *mut rocksdb_writebatch_wi_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_writebatch_wi_put(
        arg1: *mut rocksdb_writebatch_wi_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_put_cf(
        arg1: *mut rocksdb_writebatch_wi_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_putv(
        b: *mut rocksdb_writebatch_wi_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        num_values: libc::c_int,
        values_list: *const *const libc::c_char,
        values_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_putv_cf(
        b: *mut rocksdb_writebatch_wi_t,
        column_family: *mut rocksdb_column_family_handle_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        num_values: libc::c_int,
        values_list: *const *const libc::c_char,
        values_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_merge(
        arg1: *mut rocksdb_writebatch_wi_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_merge_cf(
        arg1: *mut rocksdb_writebatch_wi_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_mergev(
        b: *mut rocksdb_writebatch_wi_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        num_values: libc::c_int,
        values_list: *const *const libc::c_char,
        values_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_mergev_cf(
        b: *mut rocksdb_writebatch_wi_t,
        column_family: *mut rocksdb_column_family_handle_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        num_values: libc::c_int,
        values_list: *const *const libc::c_char,
        values_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_delete(
        arg1: *mut rocksdb_writebatch_wi_t,
        key: *const libc::c_char,
        klen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_singledelete(
        arg1: *mut rocksdb_writebatch_wi_t,
        key: *const libc::c_char,
        klen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_delete_cf(
        arg1: *mut rocksdb_writebatch_wi_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_singledelete_cf(
        arg1: *mut rocksdb_writebatch_wi_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_deletev(
        b: *mut rocksdb_writebatch_wi_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_deletev_cf(
        b: *mut rocksdb_writebatch_wi_t,
        column_family: *mut rocksdb_column_family_handle_t,
        num_keys: libc::c_int,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_delete_range(
        b: *mut rocksdb_writebatch_wi_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        end_key: *const libc::c_char,
        end_key_len: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_delete_range_cf(
        b: *mut rocksdb_writebatch_wi_t,
        column_family: *mut rocksdb_column_family_handle_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        end_key: *const libc::c_char,
        end_key_len: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_delete_rangev(
        b: *mut rocksdb_writebatch_wi_t,
        num_keys: libc::c_int,
        start_keys_list: *const *const libc::c_char,
        start_keys_list_sizes: *const usize,
        end_keys_list: *const *const libc::c_char,
        end_keys_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_delete_rangev_cf(
        b: *mut rocksdb_writebatch_wi_t,
        column_family: *mut rocksdb_column_family_handle_t,
        num_keys: libc::c_int,
        start_keys_list: *const *const libc::c_char,
        start_keys_list_sizes: *const usize,
        end_keys_list: *const *const libc::c_char,
        end_keys_list_sizes: *const usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_put_log_data(
        arg1: *mut rocksdb_writebatch_wi_t,
        blob: *const libc::c_char,
        len: usize,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_iterate(
        b: *mut rocksdb_writebatch_wi_t,
        state: *mut libc::c_void,
        put: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                k: *const libc::c_char,
                klen: usize,
                v: *const libc::c_char,
                vlen: usize,
            ),
        >,
        deleted: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut libc::c_void, k: *const libc::c_char, klen: usize),
        >,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_data(
        b: *mut rocksdb_writebatch_wi_t,
        size: *mut usize,
    ) -> *const libc::c_char;
}
extern "C" {
    pub fn rocksdb_writebatch_wi_set_save_point(arg1: *mut rocksdb_writebatch_wi_t);
}
extern "C" {
    pub fn rocksdb_writebatch_wi_rollback_to_save_point(
        arg1: *mut rocksdb_writebatch_wi_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_get_from_batch(
        wbwi: *mut rocksdb_writebatch_wi_t,
        options: *const rocksdb_options_t,
        key: *const libc::c_char,
        keylen: usize,
        vallen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_writebatch_wi_get_from_batch_cf(
        wbwi: *mut rocksdb_writebatch_wi_t,
        options: *const rocksdb_options_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        vallen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_writebatch_wi_get_from_batch_and_db(
        wbwi: *mut rocksdb_writebatch_wi_t,
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        vallen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_writebatch_wi_get_from_batch_and_db_cf(
        wbwi: *mut rocksdb_writebatch_wi_t,
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        vallen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_write_writebatch_wi(
        db: *mut rocksdb_t,
        options: *const rocksdb_writeoptions_t,
        wbwi: *mut rocksdb_writebatch_wi_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_writebatch_wi_create_iterator_with_base(
        wbwi: *mut rocksdb_writebatch_wi_t,
        base_iterator: *mut rocksdb_iterator_t,
    ) -> *mut rocksdb_iterator_t;
}
extern "C" {
    pub fn rocksdb_writebatch_wi_create_iterator_with_base_cf(
        wbwi: *mut rocksdb_writebatch_wi_t,
        base_iterator: *mut rocksdb_iterator_t,
        cf: *mut rocksdb_column_family_handle_t,
    ) -> *mut rocksdb_iterator_t;
}
extern "C" {
    pub fn rocksdb_load_latest_options(
        db_path: *const libc::c_char,
        env: *mut rocksdb_env_t,
        ignore_unknown_options: bool,
        cache: *mut rocksdb_cache_t,
        db_options: *mut *mut rocksdb_options_t,
        num_column_families: *mut usize,
        column_family_names: *mut *mut *mut libc::c_char,
        column_family_options: *mut *mut *mut rocksdb_options_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_load_latest_options_destroy(
        db_options: *mut rocksdb_options_t,
        list_column_family_names: *mut *mut libc::c_char,
        list_column_family_options: *mut *mut rocksdb_options_t,
        len: usize,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_create() -> *mut rocksdb_block_based_table_options_t;
}
extern "C" {
    pub fn rocksdb_block_based_options_destroy(options: *mut rocksdb_block_based_table_options_t);
}
extern "C" {
    pub fn rocksdb_block_based_options_set_checksum(
        arg1: *mut rocksdb_block_based_table_options_t,
        arg2: libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_block_size(
        options: *mut rocksdb_block_based_table_options_t,
        block_size: usize,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_block_size_deviation(
        options: *mut rocksdb_block_based_table_options_t,
        block_size_deviation: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_block_restart_interval(
        options: *mut rocksdb_block_based_table_options_t,
        block_restart_interval: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_index_block_restart_interval(
        options: *mut rocksdb_block_based_table_options_t,
        index_block_restart_interval: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_metadata_block_size(
        options: *mut rocksdb_block_based_table_options_t,
        metadata_block_size: u64,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_partition_filters(
        options: *mut rocksdb_block_based_table_options_t,
        partition_filters: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_optimize_filters_for_memory(
        options: *mut rocksdb_block_based_table_options_t,
        optimize_filters_for_memory: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_use_delta_encoding(
        options: *mut rocksdb_block_based_table_options_t,
        use_delta_encoding: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_filter_policy(
        options: *mut rocksdb_block_based_table_options_t,
        filter_policy: *mut rocksdb_filterpolicy_t,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_no_block_cache(
        options: *mut rocksdb_block_based_table_options_t,
        no_block_cache: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_block_cache(
        options: *mut rocksdb_block_based_table_options_t,
        block_cache: *mut rocksdb_cache_t,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_whole_key_filtering(
        arg1: *mut rocksdb_block_based_table_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_format_version(
        arg1: *mut rocksdb_block_based_table_options_t,
        arg2: libc::c_int,
    );
}
pub const rocksdb_block_based_table_index_type_binary_search: _bindgen_ty_1 = 0;
pub const rocksdb_block_based_table_index_type_hash_search: _bindgen_ty_1 = 1;
pub const rocksdb_block_based_table_index_type_two_level_index_search: _bindgen_ty_1 = 2;
pub type _bindgen_ty_1 = libc::c_uint;
extern "C" {
    pub fn rocksdb_block_based_options_set_index_type(
        arg1: *mut rocksdb_block_based_table_options_t,
        arg2: libc::c_int,
    );
}
pub const rocksdb_block_based_table_data_block_index_type_binary_search: _bindgen_ty_2 = 0;
pub const rocksdb_block_based_table_data_block_index_type_binary_search_and_hash: _bindgen_ty_2 = 1;
pub type _bindgen_ty_2 = libc::c_uint;
extern "C" {
    pub fn rocksdb_block_based_options_set_data_block_index_type(
        arg1: *mut rocksdb_block_based_table_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_data_block_hash_ratio(
        options: *mut rocksdb_block_based_table_options_t,
        v: f64,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_cache_index_and_filter_blocks(
        arg1: *mut rocksdb_block_based_table_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_cache_index_and_filter_blocks_with_high_priority(
        arg1: *mut rocksdb_block_based_table_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_pin_l0_filter_and_index_blocks_in_cache(
        arg1: *mut rocksdb_block_based_table_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_block_based_options_set_pin_top_level_index_and_filter(
        arg1: *mut rocksdb_block_based_table_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_set_block_based_table_factory(
        opt: *mut rocksdb_options_t,
        table_options: *mut rocksdb_block_based_table_options_t,
    );
}
extern "C" {
    pub fn rocksdb_options_set_write_buffer_manager(
        opt: *mut rocksdb_options_t,
        wbm: *mut rocksdb_write_buffer_manager_t,
    );
}
extern "C" {
    pub fn rocksdb_cuckoo_options_create() -> *mut rocksdb_cuckoo_table_options_t;
}
extern "C" {
    pub fn rocksdb_cuckoo_options_destroy(options: *mut rocksdb_cuckoo_table_options_t);
}
extern "C" {
    pub fn rocksdb_cuckoo_options_set_hash_ratio(
        options: *mut rocksdb_cuckoo_table_options_t,
        v: f64,
    );
}
extern "C" {
    pub fn rocksdb_cuckoo_options_set_max_search_depth(
        options: *mut rocksdb_cuckoo_table_options_t,
        v: u32,
    );
}
extern "C" {
    pub fn rocksdb_cuckoo_options_set_cuckoo_block_size(
        options: *mut rocksdb_cuckoo_table_options_t,
        v: u32,
    );
}
extern "C" {
    pub fn rocksdb_cuckoo_options_set_identity_as_first_hash(
        options: *mut rocksdb_cuckoo_table_options_t,
        v: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_cuckoo_options_set_use_module_hash(
        options: *mut rocksdb_cuckoo_table_options_t,
        v: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_set_cuckoo_table_factory(
        opt: *mut rocksdb_options_t,
        table_options: *mut rocksdb_cuckoo_table_options_t,
    );
}
extern "C" {
    pub fn rocksdb_set_options(
        db: *mut rocksdb_t,
        count: libc::c_int,
        keys: *const *const libc::c_char,
        values: *const *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_set_options_cf(
        db: *mut rocksdb_t,
        handle: *mut rocksdb_column_family_handle_t,
        count: libc::c_int,
        keys: *const *const libc::c_char,
        values: *const *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_options_create() -> *mut rocksdb_options_t;
}
extern "C" {
    pub fn rocksdb_options_destroy(arg1: *mut rocksdb_options_t);
}
extern "C" {
    pub fn rocksdb_options_create_copy(arg1: *mut rocksdb_options_t) -> *mut rocksdb_options_t;
}
extern "C" {
    pub fn rocksdb_options_increase_parallelism(
        opt: *mut rocksdb_options_t,
        total_threads: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_optimize_for_point_lookup(
        opt: *mut rocksdb_options_t,
        block_cache_size_mb: u64,
    );
}
extern "C" {
    pub fn rocksdb_options_optimize_level_style_compaction(
        opt: *mut rocksdb_options_t,
        memtable_memory_budget: u64,
    );
}
extern "C" {
    pub fn rocksdb_options_optimize_universal_style_compaction(
        opt: *mut rocksdb_options_t,
        memtable_memory_budget: u64,
    );
}
extern "C" {
    pub fn rocksdb_options_set_allow_ingest_behind(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_allow_ingest_behind(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_compaction_filter(
        arg1: *mut rocksdb_options_t,
        arg2: *mut rocksdb_compactionfilter_t,
    );
}
extern "C" {
    pub fn rocksdb_options_set_compaction_filter_factory(
        arg1: *mut rocksdb_options_t,
        arg2: *mut rocksdb_compactionfilterfactory_t,
    );
}
extern "C" {
    pub fn rocksdb_options_compaction_readahead_size(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_compaction_readahead_size(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_comparator(
        arg1: *mut rocksdb_options_t,
        arg2: *mut rocksdb_comparator_t,
    );
}
extern "C" {
    pub fn rocksdb_options_set_merge_operator(
        arg1: *mut rocksdb_options_t,
        arg2: *mut rocksdb_mergeoperator_t,
    );
}
extern "C" {
    pub fn rocksdb_options_set_uint64add_merge_operator(arg1: *mut rocksdb_options_t);
}
extern "C" {
    pub fn rocksdb_options_set_compression_per_level(
        opt: *mut rocksdb_options_t,
        level_values: *const libc::c_int,
        num_levels: usize,
    );
}
extern "C" {
    pub fn rocksdb_options_set_create_if_missing(arg1: *mut rocksdb_options_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_create_if_missing(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_create_missing_column_families(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_create_missing_column_families(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_error_if_exists(arg1: *mut rocksdb_options_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_error_if_exists(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_paranoid_checks(arg1: *mut rocksdb_options_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_paranoid_checks(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_db_paths(
        arg1: *mut rocksdb_options_t,
        path_values: *mut *const rocksdb_dbpath_t,
        num_paths: usize,
    );
}
extern "C" {
    pub fn rocksdb_options_set_cf_paths(
        arg1: *mut rocksdb_options_t,
        path_values: *mut *const rocksdb_dbpath_t,
        num_paths: usize,
    );
}
extern "C" {
    pub fn rocksdb_options_set_env(arg1: *mut rocksdb_options_t, arg2: *mut rocksdb_env_t);
}
extern "C" {
    pub fn rocksdb_options_set_info_log(arg1: *mut rocksdb_options_t, arg2: *mut rocksdb_logger_t);
}
extern "C" {
    pub fn rocksdb_options_set_info_log_level(arg1: *mut rocksdb_options_t, arg2: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_info_log_level(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_write_buffer_size(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_write_buffer_size(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_db_write_buffer_size(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_db_write_buffer_size(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_max_open_files(arg1: *mut rocksdb_options_t, arg2: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_max_open_files(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_max_file_opening_threads(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_max_file_opening_threads(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_max_total_wal_size(opt: *mut rocksdb_options_t, n: u64);
}
extern "C" {
    pub fn rocksdb_options_get_max_total_wal_size(opt: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_compression_options(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
        arg3: libc::c_int,
        arg4: libc::c_int,
        arg5: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_set_compression_options_zstd_max_train_bytes(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_compression_options_zstd_max_train_bytes(
        opt: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_compression_options_use_zstd_dict_trainer(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_compression_options_use_zstd_dict_trainer(
        opt: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_compression_options_parallel_threads(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_compression_options_parallel_threads(
        opt: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_compression_options_max_dict_buffer_bytes(
        arg1: *mut rocksdb_options_t,
        arg2: u64,
    );
}
extern "C" {
    pub fn rocksdb_options_get_compression_options_max_dict_buffer_bytes(
        opt: *mut rocksdb_options_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_bottommost_compression_options(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
        arg3: libc::c_int,
        arg4: libc::c_int,
        arg5: libc::c_int,
        arg6: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_set_bottommost_compression_options_zstd_max_train_bytes(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
        arg3: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_set_bottommost_compression_options_use_zstd_dict_trainer(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
        arg3: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_bottommost_compression_options_use_zstd_dict_trainer(
        opt: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_bottommost_compression_options_max_dict_buffer_bytes(
        arg1: *mut rocksdb_options_t,
        arg2: u64,
        arg3: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_set_prefix_extractor(
        arg1: *mut rocksdb_options_t,
        arg2: *mut rocksdb_slicetransform_t,
    );
}
extern "C" {
    pub fn rocksdb_options_set_num_levels(arg1: *mut rocksdb_options_t, arg2: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_num_levels(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_level0_file_num_compaction_trigger(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_level0_file_num_compaction_trigger(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_level0_slowdown_writes_trigger(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_level0_slowdown_writes_trigger(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_level0_stop_writes_trigger(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_level0_stop_writes_trigger(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_target_file_size_base(arg1: *mut rocksdb_options_t, arg2: u64);
}
extern "C" {
    pub fn rocksdb_options_get_target_file_size_base(arg1: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_target_file_size_multiplier(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_target_file_size_multiplier(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_max_bytes_for_level_base(arg1: *mut rocksdb_options_t, arg2: u64);
}
extern "C" {
    pub fn rocksdb_options_get_max_bytes_for_level_base(arg1: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_level_compaction_dynamic_level_bytes(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_level_compaction_dynamic_level_bytes(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_max_bytes_for_level_multiplier(
        arg1: *mut rocksdb_options_t,
        arg2: f64,
    );
}
extern "C" {
    pub fn rocksdb_options_get_max_bytes_for_level_multiplier(arg1: *mut rocksdb_options_t) -> f64;
}
extern "C" {
    pub fn rocksdb_options_set_max_bytes_for_level_multiplier_additional(
        arg1: *mut rocksdb_options_t,
        level_values: *mut libc::c_int,
        num_levels: usize,
    );
}
extern "C" {
    pub fn rocksdb_options_enable_statistics(arg1: *mut rocksdb_options_t);
}
extern "C" {
    pub fn rocksdb_options_set_periodic_compaction_seconds(arg1: *mut rocksdb_options_t, arg2: u64);
}
extern "C" {
    pub fn rocksdb_options_get_periodic_compaction_seconds(arg1: *mut rocksdb_options_t) -> u64;
}
pub const rocksdb_statistics_level_disable_all: _bindgen_ty_3 = 0;
pub const rocksdb_statistics_level_except_tickers: _bindgen_ty_3 = 0;
pub const rocksdb_statistics_level_except_histogram_or_timers: _bindgen_ty_3 = 1;
pub const rocksdb_statistics_level_except_timers: _bindgen_ty_3 = 2;
pub const rocksdb_statistics_level_except_detailed_timers: _bindgen_ty_3 = 3;
pub const rocksdb_statistics_level_except_time_for_mutex: _bindgen_ty_3 = 4;
pub const rocksdb_statistics_level_all: _bindgen_ty_3 = 5;
pub type _bindgen_ty_3 = libc::c_uint;
extern "C" {
    pub fn rocksdb_options_set_statistics_level(arg1: *mut rocksdb_options_t, level: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_statistics_level(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_skip_stats_update_on_db_open(
        opt: *mut rocksdb_options_t,
        val: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_skip_stats_update_on_db_open(
        opt: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_skip_checking_sst_file_sizes_on_db_open(
        opt: *mut rocksdb_options_t,
        val: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_skip_checking_sst_file_sizes_on_db_open(
        opt: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_enable_blob_files(opt: *mut rocksdb_options_t, val: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_enable_blob_files(opt: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_min_blob_size(opt: *mut rocksdb_options_t, val: u64);
}
extern "C" {
    pub fn rocksdb_options_get_min_blob_size(opt: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_blob_file_size(opt: *mut rocksdb_options_t, val: u64);
}
extern "C" {
    pub fn rocksdb_options_get_blob_file_size(opt: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_blob_compression_type(opt: *mut rocksdb_options_t, val: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_blob_compression_type(opt: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_enable_blob_gc(opt: *mut rocksdb_options_t, val: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_enable_blob_gc(opt: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_blob_gc_age_cutoff(opt: *mut rocksdb_options_t, val: f64);
}
extern "C" {
    pub fn rocksdb_options_get_blob_gc_age_cutoff(opt: *mut rocksdb_options_t) -> f64;
}
extern "C" {
    pub fn rocksdb_options_set_blob_gc_force_threshold(opt: *mut rocksdb_options_t, val: f64);
}
extern "C" {
    pub fn rocksdb_options_get_blob_gc_force_threshold(opt: *mut rocksdb_options_t) -> f64;
}
extern "C" {
    pub fn rocksdb_options_set_blob_compaction_readahead_size(
        opt: *mut rocksdb_options_t,
        val: u64,
    );
}
extern "C" {
    pub fn rocksdb_options_get_blob_compaction_readahead_size(opt: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_blob_file_starting_level(
        opt: *mut rocksdb_options_t,
        val: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_blob_file_starting_level(opt: *mut rocksdb_options_t)
        -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_blob_cache(
        opt: *mut rocksdb_options_t,
        blob_cache: *mut rocksdb_cache_t,
    );
}
pub const rocksdb_prepopulate_blob_disable: _bindgen_ty_4 = 0;
pub const rocksdb_prepopulate_blob_flush_only: _bindgen_ty_4 = 1;
pub type _bindgen_ty_4 = libc::c_uint;
extern "C" {
    pub fn rocksdb_options_set_prepopulate_blob_cache(
        opt: *mut rocksdb_options_t,
        val: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_prepopulate_blob_cache(opt: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_statistics_get_string(opt: *mut rocksdb_options_t) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_options_statistics_get_ticker_count(
        opt: *mut rocksdb_options_t,
        ticker_type: u32,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_options_statistics_get_histogram_data(
        opt: *mut rocksdb_options_t,
        histogram_type: u32,
        data: *mut rocksdb_statistics_histogram_data_t,
    );
}
extern "C" {
    pub fn rocksdb_options_set_max_write_buffer_number(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_max_write_buffer_number(arg1: *mut rocksdb_options_t)
        -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_min_write_buffer_number_to_merge(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_min_write_buffer_number_to_merge(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_max_write_buffer_number_to_maintain(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_max_write_buffer_number_to_maintain(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_max_write_buffer_size_to_maintain(
        arg1: *mut rocksdb_options_t,
        arg2: i64,
    );
}
extern "C" {
    pub fn rocksdb_options_get_max_write_buffer_size_to_maintain(
        arg1: *mut rocksdb_options_t,
    ) -> i64;
}
extern "C" {
    pub fn rocksdb_options_set_enable_pipelined_write(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_enable_pipelined_write(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_unordered_write(arg1: *mut rocksdb_options_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_unordered_write(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_max_subcompactions(arg1: *mut rocksdb_options_t, arg2: u32);
}
extern "C" {
    pub fn rocksdb_options_get_max_subcompactions(arg1: *mut rocksdb_options_t) -> u32;
}
extern "C" {
    pub fn rocksdb_options_set_max_background_jobs(arg1: *mut rocksdb_options_t, arg2: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_max_background_jobs(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_max_background_compactions(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_max_background_compactions(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_max_background_flushes(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_max_background_flushes(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_max_log_file_size(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_max_log_file_size(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_log_file_time_to_roll(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_log_file_time_to_roll(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_keep_log_file_num(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_keep_log_file_num(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_recycle_log_file_num(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_recycle_log_file_num(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_soft_pending_compaction_bytes_limit(
        opt: *mut rocksdb_options_t,
        v: usize,
    );
}
extern "C" {
    pub fn rocksdb_options_get_soft_pending_compaction_bytes_limit(
        opt: *mut rocksdb_options_t,
    ) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_hard_pending_compaction_bytes_limit(
        opt: *mut rocksdb_options_t,
        v: usize,
    );
}
extern "C" {
    pub fn rocksdb_options_get_hard_pending_compaction_bytes_limit(
        opt: *mut rocksdb_options_t,
    ) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_max_manifest_file_size(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_max_manifest_file_size(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_table_cache_numshardbits(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_table_cache_numshardbits(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_arena_block_size(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_arena_block_size(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_use_fsync(arg1: *mut rocksdb_options_t, arg2: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_use_fsync(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_db_log_dir(arg1: *mut rocksdb_options_t, arg2: *const libc::c_char);
}
extern "C" {
    pub fn rocksdb_options_set_wal_dir(arg1: *mut rocksdb_options_t, arg2: *const libc::c_char);
}
extern "C" {
    pub fn rocksdb_options_set_WAL_ttl_seconds(arg1: *mut rocksdb_options_t, arg2: u64);
}
extern "C" {
    pub fn rocksdb_options_get_WAL_ttl_seconds(arg1: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_WAL_size_limit_MB(arg1: *mut rocksdb_options_t, arg2: u64);
}
extern "C" {
    pub fn rocksdb_options_get_WAL_size_limit_MB(arg1: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_manifest_preallocation_size(
        arg1: *mut rocksdb_options_t,
        arg2: usize,
    );
}
extern "C" {
    pub fn rocksdb_options_get_manifest_preallocation_size(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_allow_mmap_reads(arg1: *mut rocksdb_options_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_allow_mmap_reads(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_allow_mmap_writes(arg1: *mut rocksdb_options_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_allow_mmap_writes(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_use_direct_reads(arg1: *mut rocksdb_options_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_use_direct_reads(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_use_direct_io_for_flush_and_compaction(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_use_direct_io_for_flush_and_compaction(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_is_fd_close_on_exec(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_is_fd_close_on_exec(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_stats_dump_period_sec(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uint,
    );
}
extern "C" {
    pub fn rocksdb_options_get_stats_dump_period_sec(arg1: *mut rocksdb_options_t) -> libc::c_uint;
}
extern "C" {
    pub fn rocksdb_options_set_stats_persist_period_sec(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uint,
    );
}
extern "C" {
    pub fn rocksdb_options_get_stats_persist_period_sec(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uint;
}
extern "C" {
    pub fn rocksdb_options_set_advise_random_on_open(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_advise_random_on_open(arg1: *mut rocksdb_options_t)
        -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_access_hint_on_compaction_start(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_access_hint_on_compaction_start(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_use_adaptive_mutex(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_use_adaptive_mutex(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_bytes_per_sync(arg1: *mut rocksdb_options_t, arg2: u64);
}
extern "C" {
    pub fn rocksdb_options_get_bytes_per_sync(arg1: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_wal_bytes_per_sync(arg1: *mut rocksdb_options_t, arg2: u64);
}
extern "C" {
    pub fn rocksdb_options_get_wal_bytes_per_sync(arg1: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_writable_file_max_buffer_size(
        arg1: *mut rocksdb_options_t,
        arg2: u64,
    );
}
extern "C" {
    pub fn rocksdb_options_get_writable_file_max_buffer_size(arg1: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_allow_concurrent_memtable_write(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_allow_concurrent_memtable_write(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_enable_write_thread_adaptive_yield(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_enable_write_thread_adaptive_yield(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_max_sequential_skip_in_iterations(
        arg1: *mut rocksdb_options_t,
        arg2: u64,
    );
}
extern "C" {
    pub fn rocksdb_options_get_max_sequential_skip_in_iterations(
        arg1: *mut rocksdb_options_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_disable_auto_compactions(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_disable_auto_compactions(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_optimize_filters_for_hits(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_optimize_filters_for_hits(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_delete_obsolete_files_period_micros(
        arg1: *mut rocksdb_options_t,
        arg2: u64,
    );
}
extern "C" {
    pub fn rocksdb_options_get_delete_obsolete_files_period_micros(
        arg1: *mut rocksdb_options_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_options_prepare_for_bulk_load(arg1: *mut rocksdb_options_t);
}
extern "C" {
    pub fn rocksdb_options_set_memtable_vector_rep(arg1: *mut rocksdb_options_t);
}
extern "C" {
    pub fn rocksdb_options_set_memtable_prefix_bloom_size_ratio(
        arg1: *mut rocksdb_options_t,
        arg2: f64,
    );
}
extern "C" {
    pub fn rocksdb_options_get_memtable_prefix_bloom_size_ratio(
        arg1: *mut rocksdb_options_t,
    ) -> f64;
}
extern "C" {
    pub fn rocksdb_options_set_max_compaction_bytes(arg1: *mut rocksdb_options_t, arg2: u64);
}
extern "C" {
    pub fn rocksdb_options_get_max_compaction_bytes(arg1: *mut rocksdb_options_t) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_hash_skip_list_rep(
        arg1: *mut rocksdb_options_t,
        arg2: usize,
        arg3: i32,
        arg4: i32,
    );
}
extern "C" {
    pub fn rocksdb_options_set_hash_link_list_rep(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_set_plain_table_factory(
        arg1: *mut rocksdb_options_t,
        arg2: u32,
        arg3: libc::c_int,
        arg4: f64,
        arg5: usize,
        arg6: usize,
        arg7: libc::c_char,
        arg8: libc::c_uchar,
        arg9: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_set_min_level_to_compress(
        opt: *mut rocksdb_options_t,
        level: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_set_memtable_huge_page_size(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_memtable_huge_page_size(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_max_successive_merges(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_max_successive_merges(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_bloom_locality(arg1: *mut rocksdb_options_t, arg2: u32);
}
extern "C" {
    pub fn rocksdb_options_get_bloom_locality(arg1: *mut rocksdb_options_t) -> u32;
}
extern "C" {
    pub fn rocksdb_options_set_inplace_update_support(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_inplace_update_support(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_inplace_update_num_locks(arg1: *mut rocksdb_options_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_options_get_inplace_update_num_locks(arg1: *mut rocksdb_options_t) -> usize;
}
extern "C" {
    pub fn rocksdb_options_set_report_bg_io_stats(arg1: *mut rocksdb_options_t, arg2: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_report_bg_io_stats(arg1: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_avoid_unnecessary_blocking_io(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_options_get_avoid_unnecessary_blocking_io(
        arg1: *mut rocksdb_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_experimental_mempurge_threshold(
        arg1: *mut rocksdb_options_t,
        arg2: f64,
    );
}
extern "C" {
    pub fn rocksdb_options_get_experimental_mempurge_threshold(arg1: *mut rocksdb_options_t)
        -> f64;
}
pub const rocksdb_tolerate_corrupted_tail_records_recovery: _bindgen_ty_5 = 0;
pub const rocksdb_absolute_consistency_recovery: _bindgen_ty_5 = 1;
pub const rocksdb_point_in_time_recovery: _bindgen_ty_5 = 2;
pub const rocksdb_skip_any_corrupted_records_recovery: _bindgen_ty_5 = 3;
pub type _bindgen_ty_5 = libc::c_uint;
extern "C" {
    pub fn rocksdb_options_set_wal_recovery_mode(arg1: *mut rocksdb_options_t, arg2: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_wal_recovery_mode(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
pub const rocksdb_no_compression: _bindgen_ty_6 = 0;
pub const rocksdb_snappy_compression: _bindgen_ty_6 = 1;
pub const rocksdb_zlib_compression: _bindgen_ty_6 = 2;
pub const rocksdb_bz2_compression: _bindgen_ty_6 = 3;
pub const rocksdb_lz4_compression: _bindgen_ty_6 = 4;
pub const rocksdb_lz4hc_compression: _bindgen_ty_6 = 5;
pub const rocksdb_xpress_compression: _bindgen_ty_6 = 6;
pub const rocksdb_zstd_compression: _bindgen_ty_6 = 7;
pub type _bindgen_ty_6 = libc::c_uint;
extern "C" {
    pub fn rocksdb_options_set_compression(arg1: *mut rocksdb_options_t, arg2: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_compression(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_bottommost_compression(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_options_get_bottommost_compression(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
pub const rocksdb_level_compaction: _bindgen_ty_7 = 0;
pub const rocksdb_universal_compaction: _bindgen_ty_7 = 1;
pub const rocksdb_fifo_compaction: _bindgen_ty_7 = 2;
pub type _bindgen_ty_7 = libc::c_uint;
extern "C" {
    pub fn rocksdb_options_set_compaction_style(arg1: *mut rocksdb_options_t, arg2: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_compaction_style(arg1: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_options_set_universal_compaction_options(
        arg1: *mut rocksdb_options_t,
        arg2: *mut rocksdb_universal_compaction_options_t,
    );
}
extern "C" {
    pub fn rocksdb_options_set_fifo_compaction_options(
        opt: *mut rocksdb_options_t,
        fifo: *mut rocksdb_fifo_compaction_options_t,
    );
}
extern "C" {
    pub fn rocksdb_options_set_ratelimiter(
        opt: *mut rocksdb_options_t,
        limiter: *mut rocksdb_ratelimiter_t,
    );
}
extern "C" {
    pub fn rocksdb_options_set_atomic_flush(opt: *mut rocksdb_options_t, arg1: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_atomic_flush(opt: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_row_cache(opt: *mut rocksdb_options_t, cache: *mut rocksdb_cache_t);
}
extern "C" {
    pub fn rocksdb_options_add_compact_on_deletion_collector_factory(
        arg1: *mut rocksdb_options_t,
        window_size: usize,
        num_dels_trigger: usize,
    );
}
extern "C" {
    pub fn rocksdb_options_add_compact_on_deletion_collector_factory_del_ratio(
        arg1: *mut rocksdb_options_t,
        window_size: usize,
        num_dels_trigger: usize,
        deletion_ratio: f64,
    );
}
extern "C" {
    pub fn rocksdb_options_set_manual_wal_flush(opt: *mut rocksdb_options_t, arg1: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_get_manual_wal_flush(opt: *mut rocksdb_options_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_options_set_wal_compression(opt: *mut rocksdb_options_t, arg1: libc::c_int);
}
extern "C" {
    pub fn rocksdb_options_get_wal_compression(opt: *mut rocksdb_options_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_ratelimiter_create(
        rate_bytes_per_sec: i64,
        refill_period_us: i64,
        fairness: i32,
    ) -> *mut rocksdb_ratelimiter_t;
}
extern "C" {
    pub fn rocksdb_ratelimiter_create_auto_tuned(
        rate_bytes_per_sec: i64,
        refill_period_us: i64,
        fairness: i32,
    ) -> *mut rocksdb_ratelimiter_t;
}
extern "C" {
    pub fn rocksdb_ratelimiter_destroy(arg1: *mut rocksdb_ratelimiter_t);
}
pub const rocksdb_uninitialized: _bindgen_ty_8 = 0;
pub const rocksdb_disable: _bindgen_ty_8 = 1;
pub const rocksdb_enable_count: _bindgen_ty_8 = 2;
pub const rocksdb_enable_time_except_for_mutex: _bindgen_ty_8 = 3;
pub const rocksdb_enable_time: _bindgen_ty_8 = 4;
pub const rocksdb_out_of_bounds: _bindgen_ty_8 = 5;
pub type _bindgen_ty_8 = libc::c_uint;
pub const rocksdb_user_key_comparison_count: _bindgen_ty_9 = 0;
pub const rocksdb_block_cache_hit_count: _bindgen_ty_9 = 1;
pub const rocksdb_block_read_count: _bindgen_ty_9 = 2;
pub const rocksdb_block_read_byte: _bindgen_ty_9 = 3;
pub const rocksdb_block_read_time: _bindgen_ty_9 = 4;
pub const rocksdb_block_checksum_time: _bindgen_ty_9 = 5;
pub const rocksdb_block_decompress_time: _bindgen_ty_9 = 6;
pub const rocksdb_get_read_bytes: _bindgen_ty_9 = 7;
pub const rocksdb_multiget_read_bytes: _bindgen_ty_9 = 8;
pub const rocksdb_iter_read_bytes: _bindgen_ty_9 = 9;
pub const rocksdb_internal_key_skipped_count: _bindgen_ty_9 = 10;
pub const rocksdb_internal_delete_skipped_count: _bindgen_ty_9 = 11;
pub const rocksdb_internal_recent_skipped_count: _bindgen_ty_9 = 12;
pub const rocksdb_internal_merge_count: _bindgen_ty_9 = 13;
pub const rocksdb_get_snapshot_time: _bindgen_ty_9 = 14;
pub const rocksdb_get_from_memtable_time: _bindgen_ty_9 = 15;
pub const rocksdb_get_from_memtable_count: _bindgen_ty_9 = 16;
pub const rocksdb_get_post_process_time: _bindgen_ty_9 = 17;
pub const rocksdb_get_from_output_files_time: _bindgen_ty_9 = 18;
pub const rocksdb_seek_on_memtable_time: _bindgen_ty_9 = 19;
pub const rocksdb_seek_on_memtable_count: _bindgen_ty_9 = 20;
pub const rocksdb_next_on_memtable_count: _bindgen_ty_9 = 21;
pub const rocksdb_prev_on_memtable_count: _bindgen_ty_9 = 22;
pub const rocksdb_seek_child_seek_time: _bindgen_ty_9 = 23;
pub const rocksdb_seek_child_seek_count: _bindgen_ty_9 = 24;
pub const rocksdb_seek_min_heap_time: _bindgen_ty_9 = 25;
pub const rocksdb_seek_max_heap_time: _bindgen_ty_9 = 26;
pub const rocksdb_seek_internal_seek_time: _bindgen_ty_9 = 27;
pub const rocksdb_find_next_user_entry_time: _bindgen_ty_9 = 28;
pub const rocksdb_write_wal_time: _bindgen_ty_9 = 29;
pub const rocksdb_write_memtable_time: _bindgen_ty_9 = 30;
pub const rocksdb_write_delay_time: _bindgen_ty_9 = 31;
pub const rocksdb_write_pre_and_post_process_time: _bindgen_ty_9 = 32;
pub const rocksdb_db_mutex_lock_nanos: _bindgen_ty_9 = 33;
pub const rocksdb_db_condition_wait_nanos: _bindgen_ty_9 = 34;
pub const rocksdb_merge_operator_time_nanos: _bindgen_ty_9 = 35;
pub const rocksdb_read_index_block_nanos: _bindgen_ty_9 = 36;
pub const rocksdb_read_filter_block_nanos: _bindgen_ty_9 = 37;
pub const rocksdb_new_table_block_iter_nanos: _bindgen_ty_9 = 38;
pub const rocksdb_new_table_iterator_nanos: _bindgen_ty_9 = 39;
pub const rocksdb_block_seek_nanos: _bindgen_ty_9 = 40;
pub const rocksdb_find_table_nanos: _bindgen_ty_9 = 41;
pub const rocksdb_bloom_memtable_hit_count: _bindgen_ty_9 = 42;
pub const rocksdb_bloom_memtable_miss_count: _bindgen_ty_9 = 43;
pub const rocksdb_bloom_sst_hit_count: _bindgen_ty_9 = 44;
pub const rocksdb_bloom_sst_miss_count: _bindgen_ty_9 = 45;
pub const rocksdb_key_lock_wait_time: _bindgen_ty_9 = 46;
pub const rocksdb_key_lock_wait_count: _bindgen_ty_9 = 47;
pub const rocksdb_env_new_sequential_file_nanos: _bindgen_ty_9 = 48;
pub const rocksdb_env_new_random_access_file_nanos: _bindgen_ty_9 = 49;
pub const rocksdb_env_new_writable_file_nanos: _bindgen_ty_9 = 50;
pub const rocksdb_env_reuse_writable_file_nanos: _bindgen_ty_9 = 51;
pub const rocksdb_env_new_random_rw_file_nanos: _bindgen_ty_9 = 52;
pub const rocksdb_env_new_directory_nanos: _bindgen_ty_9 = 53;
pub const rocksdb_env_file_exists_nanos: _bindgen_ty_9 = 54;
pub const rocksdb_env_get_children_nanos: _bindgen_ty_9 = 55;
pub const rocksdb_env_get_children_file_attributes_nanos: _bindgen_ty_9 = 56;
pub const rocksdb_env_delete_file_nanos: _bindgen_ty_9 = 57;
pub const rocksdb_env_create_dir_nanos: _bindgen_ty_9 = 58;
pub const rocksdb_env_create_dir_if_missing_nanos: _bindgen_ty_9 = 59;
pub const rocksdb_env_delete_dir_nanos: _bindgen_ty_9 = 60;
pub const rocksdb_env_get_file_size_nanos: _bindgen_ty_9 = 61;
pub const rocksdb_env_get_file_modification_time_nanos: _bindgen_ty_9 = 62;
pub const rocksdb_env_rename_file_nanos: _bindgen_ty_9 = 63;
pub const rocksdb_env_link_file_nanos: _bindgen_ty_9 = 64;
pub const rocksdb_env_lock_file_nanos: _bindgen_ty_9 = 65;
pub const rocksdb_env_unlock_file_nanos: _bindgen_ty_9 = 66;
pub const rocksdb_env_new_logger_nanos: _bindgen_ty_9 = 67;
pub const rocksdb_number_async_seek: _bindgen_ty_9 = 68;
pub const rocksdb_blob_cache_hit_count: _bindgen_ty_9 = 69;
pub const rocksdb_blob_read_count: _bindgen_ty_9 = 70;
pub const rocksdb_blob_read_byte: _bindgen_ty_9 = 71;
pub const rocksdb_blob_read_time: _bindgen_ty_9 = 72;
pub const rocksdb_blob_checksum_time: _bindgen_ty_9 = 73;
pub const rocksdb_blob_decompress_time: _bindgen_ty_9 = 74;
pub const rocksdb_internal_range_del_reseek_count: _bindgen_ty_9 = 75;
pub const rocksdb_block_read_cpu_time: _bindgen_ty_9 = 76;
pub const rocksdb_total_metric_count: _bindgen_ty_9 = 79;
pub type _bindgen_ty_9 = libc::c_uint;
extern "C" {
    pub fn rocksdb_set_perf_level(arg1: libc::c_int);
}
extern "C" {
    pub fn rocksdb_perfcontext_create() -> *mut rocksdb_perfcontext_t;
}
extern "C" {
    pub fn rocksdb_perfcontext_reset(context: *mut rocksdb_perfcontext_t);
}
extern "C" {
    pub fn rocksdb_perfcontext_report(
        context: *mut rocksdb_perfcontext_t,
        exclude_zero_counters: libc::c_uchar,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_perfcontext_metric(
        context: *mut rocksdb_perfcontext_t,
        metric: libc::c_int,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_perfcontext_destroy(context: *mut rocksdb_perfcontext_t);
}
extern "C" {
    pub fn rocksdb_compactionfilter_create(
        state: *mut libc::c_void,
        destructor: ::std::option::Option<unsafe extern "C" fn(arg1: *mut libc::c_void)>,
        filter: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                level: libc::c_int,
                key: *const libc::c_char,
                key_length: usize,
                existing_value: *const libc::c_char,
                value_length: usize,
                new_value: *mut *mut libc::c_char,
                new_value_length: *mut usize,
                value_changed: *mut libc::c_uchar,
            ) -> libc::c_uchar,
        >,
        name: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut libc::c_void) -> *const libc::c_char,
        >,
    ) -> *mut rocksdb_compactionfilter_t;
}
extern "C" {
    pub fn rocksdb_compactionfilter_set_ignore_snapshots(
        arg1: *mut rocksdb_compactionfilter_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_compactionfilter_destroy(arg1: *mut rocksdb_compactionfilter_t);
}
extern "C" {
    pub fn rocksdb_compactionfiltercontext_is_full_compaction(
        context: *mut rocksdb_compactionfiltercontext_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_compactionfiltercontext_is_manual_compaction(
        context: *mut rocksdb_compactionfiltercontext_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_compactionfilterfactory_create(
        state: *mut libc::c_void,
        destructor: ::std::option::Option<unsafe extern "C" fn(arg1: *mut libc::c_void)>,
        create_compaction_filter: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                context: *mut rocksdb_compactionfiltercontext_t,
            ) -> *mut rocksdb_compactionfilter_t,
        >,
        name: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut libc::c_void) -> *const libc::c_char,
        >,
    ) -> *mut rocksdb_compactionfilterfactory_t;
}
extern "C" {
    pub fn rocksdb_compactionfilterfactory_destroy(arg1: *mut rocksdb_compactionfilterfactory_t);
}
extern "C" {
    pub fn rocksdb_comparator_create(
        state: *mut libc::c_void,
        destructor: ::std::option::Option<unsafe extern "C" fn(arg1: *mut libc::c_void)>,
        compare: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                a: *const libc::c_char,
                alen: usize,
                b: *const libc::c_char,
                blen: usize,
            ) -> libc::c_int,
        >,
        name: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut libc::c_void) -> *const libc::c_char,
        >,
    ) -> *mut rocksdb_comparator_t;
}
extern "C" {
    pub fn rocksdb_comparator_destroy(arg1: *mut rocksdb_comparator_t);
}
extern "C" {
    pub fn rocksdb_comparator_with_ts_create(
        state: *mut libc::c_void,
        destructor: ::std::option::Option<unsafe extern "C" fn(arg1: *mut libc::c_void)>,
        compare: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                a: *const libc::c_char,
                alen: usize,
                b: *const libc::c_char,
                blen: usize,
            ) -> libc::c_int,
        >,
        compare_ts: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                a_ts: *const libc::c_char,
                a_tslen: usize,
                b_ts: *const libc::c_char,
                b_tslen: usize,
            ) -> libc::c_int,
        >,
        compare_without_ts: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                a: *const libc::c_char,
                alen: usize,
                a_has_ts: libc::c_uchar,
                b: *const libc::c_char,
                blen: usize,
                b_has_ts: libc::c_uchar,
            ) -> libc::c_int,
        >,
        name: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut libc::c_void) -> *const libc::c_char,
        >,
        timestamp_size: usize,
    ) -> *mut rocksdb_comparator_t;
}
extern "C" {
    pub fn rocksdb_filterpolicy_destroy(arg1: *mut rocksdb_filterpolicy_t);
}
extern "C" {
    pub fn rocksdb_filterpolicy_create_bloom(bits_per_key: f64) -> *mut rocksdb_filterpolicy_t;
}
extern "C" {
    pub fn rocksdb_filterpolicy_create_bloom_full(bits_per_key: f64)
        -> *mut rocksdb_filterpolicy_t;
}
extern "C" {
    pub fn rocksdb_filterpolicy_create_ribbon(
        bloom_equivalent_bits_per_key: f64,
    ) -> *mut rocksdb_filterpolicy_t;
}
extern "C" {
    pub fn rocksdb_filterpolicy_create_ribbon_hybrid(
        bloom_equivalent_bits_per_key: f64,
        bloom_before_level: libc::c_int,
    ) -> *mut rocksdb_filterpolicy_t;
}
extern "C" {
    pub fn rocksdb_mergeoperator_create(
        state: *mut libc::c_void,
        destructor: ::std::option::Option<unsafe extern "C" fn(arg1: *mut libc::c_void)>,
        full_merge: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                key: *const libc::c_char,
                key_length: usize,
                existing_value: *const libc::c_char,
                existing_value_length: usize,
                operands_list: *const *const libc::c_char,
                operands_list_length: *const usize,
                num_operands: libc::c_int,
                success: *mut libc::c_uchar,
                new_value_length: *mut usize,
            ) -> *mut libc::c_char,
        >,
        partial_merge: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                key: *const libc::c_char,
                key_length: usize,
                operands_list: *const *const libc::c_char,
                operands_list_length: *const usize,
                num_operands: libc::c_int,
                success: *mut libc::c_uchar,
                new_value_length: *mut usize,
            ) -> *mut libc::c_char,
        >,
        delete_value: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                value: *const libc::c_char,
                value_length: usize,
            ),
        >,
        name: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut libc::c_void) -> *const libc::c_char,
        >,
    ) -> *mut rocksdb_mergeoperator_t;
}
extern "C" {
    pub fn rocksdb_mergeoperator_destroy(arg1: *mut rocksdb_mergeoperator_t);
}
extern "C" {
    pub fn rocksdb_readoptions_create() -> *mut rocksdb_readoptions_t;
}
extern "C" {
    pub fn rocksdb_readoptions_destroy(arg1: *mut rocksdb_readoptions_t);
}
extern "C" {
    pub fn rocksdb_readoptions_set_verify_checksums(
        arg1: *mut rocksdb_readoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_get_verify_checksums(
        arg1: *mut rocksdb_readoptions_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_readoptions_set_fill_cache(
        arg1: *mut rocksdb_readoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_get_fill_cache(arg1: *mut rocksdb_readoptions_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_readoptions_set_snapshot(
        arg1: *mut rocksdb_readoptions_t,
        arg2: *const rocksdb_snapshot_t,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_set_iterate_upper_bound(
        arg1: *mut rocksdb_readoptions_t,
        key: *const libc::c_char,
        keylen: usize,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_set_iterate_lower_bound(
        arg1: *mut rocksdb_readoptions_t,
        key: *const libc::c_char,
        keylen: usize,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_set_read_tier(arg1: *mut rocksdb_readoptions_t, arg2: libc::c_int);
}
extern "C" {
    pub fn rocksdb_readoptions_get_read_tier(arg1: *mut rocksdb_readoptions_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_readoptions_set_tailing(arg1: *mut rocksdb_readoptions_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_readoptions_get_tailing(arg1: *mut rocksdb_readoptions_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_readoptions_set_managed(arg1: *mut rocksdb_readoptions_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_readoptions_set_readahead_size(arg1: *mut rocksdb_readoptions_t, arg2: usize);
}
extern "C" {
    pub fn rocksdb_readoptions_get_readahead_size(arg1: *mut rocksdb_readoptions_t) -> usize;
}
extern "C" {
    pub fn rocksdb_readoptions_set_prefix_same_as_start(
        arg1: *mut rocksdb_readoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_get_prefix_same_as_start(
        arg1: *mut rocksdb_readoptions_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_readoptions_set_pin_data(arg1: *mut rocksdb_readoptions_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_readoptions_get_pin_data(arg1: *mut rocksdb_readoptions_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_readoptions_set_total_order_seek(
        arg1: *mut rocksdb_readoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_get_total_order_seek(
        arg1: *mut rocksdb_readoptions_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_readoptions_set_max_skippable_internal_keys(
        arg1: *mut rocksdb_readoptions_t,
        arg2: u64,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_get_max_skippable_internal_keys(
        arg1: *mut rocksdb_readoptions_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_readoptions_set_background_purge_on_iterator_cleanup(
        arg1: *mut rocksdb_readoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_get_background_purge_on_iterator_cleanup(
        arg1: *mut rocksdb_readoptions_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_readoptions_set_ignore_range_deletions(
        arg1: *mut rocksdb_readoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_get_ignore_range_deletions(
        arg1: *mut rocksdb_readoptions_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_readoptions_set_deadline(arg1: *mut rocksdb_readoptions_t, microseconds: u64);
}
extern "C" {
    pub fn rocksdb_readoptions_get_deadline(arg1: *mut rocksdb_readoptions_t) -> u64;
}
extern "C" {
    pub fn rocksdb_readoptions_set_io_timeout(arg1: *mut rocksdb_readoptions_t, microseconds: u64);
}
extern "C" {
    pub fn rocksdb_readoptions_get_io_timeout(arg1: *mut rocksdb_readoptions_t) -> u64;
}
extern "C" {
    pub fn rocksdb_readoptions_set_async_io(arg1: *mut rocksdb_readoptions_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_readoptions_get_async_io(arg1: *mut rocksdb_readoptions_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_readoptions_set_timestamp(
        arg1: *mut rocksdb_readoptions_t,
        ts: *const libc::c_char,
        tslen: usize,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_set_iter_start_ts(
        arg1: *mut rocksdb_readoptions_t,
        ts: *const libc::c_char,
        tslen: usize,
    );
}
extern "C" {
    pub fn rocksdb_readoptions_set_auto_readahead_size(
        arg1: *mut rocksdb_readoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_writeoptions_create() -> *mut rocksdb_writeoptions_t;
}
extern "C" {
    pub fn rocksdb_writeoptions_destroy(arg1: *mut rocksdb_writeoptions_t);
}
extern "C" {
    pub fn rocksdb_writeoptions_set_sync(arg1: *mut rocksdb_writeoptions_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_writeoptions_get_sync(arg1: *mut rocksdb_writeoptions_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_writeoptions_disable_WAL(opt: *mut rocksdb_writeoptions_t, disable: libc::c_int);
}
extern "C" {
    pub fn rocksdb_writeoptions_get_disable_WAL(opt: *mut rocksdb_writeoptions_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_writeoptions_set_ignore_missing_column_families(
        arg1: *mut rocksdb_writeoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_writeoptions_get_ignore_missing_column_families(
        arg1: *mut rocksdb_writeoptions_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_writeoptions_set_no_slowdown(
        arg1: *mut rocksdb_writeoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_writeoptions_get_no_slowdown(arg1: *mut rocksdb_writeoptions_t)
        -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_writeoptions_set_low_pri(arg1: *mut rocksdb_writeoptions_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_writeoptions_get_low_pri(arg1: *mut rocksdb_writeoptions_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_writeoptions_set_memtable_insert_hint_per_batch(
        arg1: *mut rocksdb_writeoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_writeoptions_get_memtable_insert_hint_per_batch(
        arg1: *mut rocksdb_writeoptions_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_compactoptions_create() -> *mut rocksdb_compactoptions_t;
}
extern "C" {
    pub fn rocksdb_compactoptions_destroy(arg1: *mut rocksdb_compactoptions_t);
}
extern "C" {
    pub fn rocksdb_compactoptions_set_exclusive_manual_compaction(
        arg1: *mut rocksdb_compactoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_compactoptions_get_exclusive_manual_compaction(
        arg1: *mut rocksdb_compactoptions_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_compactoptions_set_bottommost_level_compaction(
        arg1: *mut rocksdb_compactoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_compactoptions_get_bottommost_level_compaction(
        arg1: *mut rocksdb_compactoptions_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_compactoptions_set_change_level(
        arg1: *mut rocksdb_compactoptions_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_compactoptions_get_change_level(
        arg1: *mut rocksdb_compactoptions_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_compactoptions_set_target_level(
        arg1: *mut rocksdb_compactoptions_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_compactoptions_get_target_level(
        arg1: *mut rocksdb_compactoptions_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_compactoptions_set_full_history_ts_low(
        arg1: *mut rocksdb_compactoptions_t,
        ts: *mut libc::c_char,
        tslen: usize,
    );
}
extern "C" {
    pub fn rocksdb_flushoptions_create() -> *mut rocksdb_flushoptions_t;
}
extern "C" {
    pub fn rocksdb_flushoptions_destroy(arg1: *mut rocksdb_flushoptions_t);
}
extern "C" {
    pub fn rocksdb_flushoptions_set_wait(arg1: *mut rocksdb_flushoptions_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_flushoptions_get_wait(arg1: *mut rocksdb_flushoptions_t) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_jemalloc_nodump_allocator_create(
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_memory_allocator_t;
}
extern "C" {
    pub fn rocksdb_memory_allocator_destroy(arg1: *mut rocksdb_memory_allocator_t);
}
extern "C" {
    pub fn rocksdb_lru_cache_options_create() -> *mut rocksdb_lru_cache_options_t;
}
extern "C" {
    pub fn rocksdb_lru_cache_options_destroy(arg1: *mut rocksdb_lru_cache_options_t);
}
extern "C" {
    pub fn rocksdb_lru_cache_options_set_capacity(
        arg1: *mut rocksdb_lru_cache_options_t,
        arg2: usize,
    );
}
extern "C" {
    pub fn rocksdb_lru_cache_options_set_num_shard_bits(
        arg1: *mut rocksdb_lru_cache_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_lru_cache_options_set_memory_allocator(
        arg1: *mut rocksdb_lru_cache_options_t,
        arg2: *mut rocksdb_memory_allocator_t,
    );
}
extern "C" {
    pub fn rocksdb_cache_create_lru(capacity: usize) -> *mut rocksdb_cache_t;
}
extern "C" {
    pub fn rocksdb_cache_create_lru_with_strict_capacity_limit(
        capacity: usize,
    ) -> *mut rocksdb_cache_t;
}
extern "C" {
    pub fn rocksdb_cache_create_lru_opts(
        arg1: *const rocksdb_lru_cache_options_t,
    ) -> *mut rocksdb_cache_t;
}
extern "C" {
    pub fn rocksdb_cache_destroy(cache: *mut rocksdb_cache_t);
}
extern "C" {
    pub fn rocksdb_cache_disown_data(cache: *mut rocksdb_cache_t);
}
extern "C" {
    pub fn rocksdb_cache_set_capacity(cache: *mut rocksdb_cache_t, capacity: usize);
}
extern "C" {
    pub fn rocksdb_cache_get_capacity(cache: *const rocksdb_cache_t) -> usize;
}
extern "C" {
    pub fn rocksdb_cache_get_usage(cache: *const rocksdb_cache_t) -> usize;
}
extern "C" {
    pub fn rocksdb_cache_get_pinned_usage(cache: *const rocksdb_cache_t) -> usize;
}
extern "C" {
    pub fn rocksdb_cache_get_table_address_count(cache: *const rocksdb_cache_t) -> usize;
}
extern "C" {
    pub fn rocksdb_cache_get_occupancy_count(cache: *const rocksdb_cache_t) -> usize;
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_create(
        buffer_size: usize,
        allow_stall: bool,
    ) -> *mut rocksdb_write_buffer_manager_t;
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_create_with_cache(
        buffer_size: usize,
        cache: *const rocksdb_cache_t,
        allow_stall: bool,
    ) -> *mut rocksdb_write_buffer_manager_t;
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_destroy(wbm: *mut rocksdb_write_buffer_manager_t);
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_enabled(wbm: *mut rocksdb_write_buffer_manager_t) -> bool;
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_cost_to_cache(
        wbm: *mut rocksdb_write_buffer_manager_t,
    ) -> bool;
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_memory_usage(
        wbm: *mut rocksdb_write_buffer_manager_t,
    ) -> usize;
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_mutable_memtable_memory_usage(
        wbm: *mut rocksdb_write_buffer_manager_t,
    ) -> usize;
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_dummy_entries_in_cache_usage(
        wbm: *mut rocksdb_write_buffer_manager_t,
    ) -> usize;
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_buffer_size(
        wbm: *mut rocksdb_write_buffer_manager_t,
    ) -> usize;
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_set_buffer_size(
        wbm: *mut rocksdb_write_buffer_manager_t,
        new_size: usize,
    );
}
extern "C" {
    pub fn rocksdb_write_buffer_manager_set_allow_stall(
        wbm: *mut rocksdb_write_buffer_manager_t,
        new_allow_stall: bool,
    );
}
extern "C" {
    pub fn rocksdb_hyper_clock_cache_options_create(
        capacity: usize,
        estimated_entry_charge: usize,
    ) -> *mut rocksdb_hyper_clock_cache_options_t;
}
extern "C" {
    pub fn rocksdb_hyper_clock_cache_options_destroy(
        arg1: *mut rocksdb_hyper_clock_cache_options_t,
    );
}
extern "C" {
    pub fn rocksdb_hyper_clock_cache_options_set_capacity(
        arg1: *mut rocksdb_hyper_clock_cache_options_t,
        arg2: usize,
    );
}
extern "C" {
    pub fn rocksdb_hyper_clock_cache_options_set_estimated_entry_charge(
        arg1: *mut rocksdb_hyper_clock_cache_options_t,
        arg2: usize,
    );
}
extern "C" {
    pub fn rocksdb_hyper_clock_cache_options_set_num_shard_bits(
        arg1: *mut rocksdb_hyper_clock_cache_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_hyper_clock_cache_options_set_memory_allocator(
        arg1: *mut rocksdb_hyper_clock_cache_options_t,
        arg2: *mut rocksdb_memory_allocator_t,
    );
}
extern "C" {
    pub fn rocksdb_cache_create_hyper_clock(
        capacity: usize,
        estimated_entry_charge: usize,
    ) -> *mut rocksdb_cache_t;
}
extern "C" {
    pub fn rocksdb_cache_create_hyper_clock_opts(
        arg1: *const rocksdb_hyper_clock_cache_options_t,
    ) -> *mut rocksdb_cache_t;
}
extern "C" {
    pub fn rocksdb_dbpath_create(
        path: *const libc::c_char,
        target_size: u64,
    ) -> *mut rocksdb_dbpath_t;
}
extern "C" {
    pub fn rocksdb_dbpath_destroy(arg1: *mut rocksdb_dbpath_t);
}
extern "C" {
    pub fn rocksdb_create_default_env() -> *mut rocksdb_env_t;
}
extern "C" {
    pub fn rocksdb_create_mem_env() -> *mut rocksdb_env_t;
}
extern "C" {
    pub fn rocksdb_env_set_background_threads(env: *mut rocksdb_env_t, n: libc::c_int);
}
extern "C" {
    pub fn rocksdb_env_get_background_threads(env: *mut rocksdb_env_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_env_set_high_priority_background_threads(
        env: *mut rocksdb_env_t,
        n: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_env_get_high_priority_background_threads(env: *mut rocksdb_env_t)
        -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_env_set_low_priority_background_threads(env: *mut rocksdb_env_t, n: libc::c_int);
}
extern "C" {
    pub fn rocksdb_env_get_low_priority_background_threads(env: *mut rocksdb_env_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_env_set_bottom_priority_background_threads(
        env: *mut rocksdb_env_t,
        n: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_env_get_bottom_priority_background_threads(
        env: *mut rocksdb_env_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_env_join_all_threads(env: *mut rocksdb_env_t);
}
extern "C" {
    pub fn rocksdb_env_lower_thread_pool_io_priority(env: *mut rocksdb_env_t);
}
extern "C" {
    pub fn rocksdb_env_lower_high_priority_thread_pool_io_priority(env: *mut rocksdb_env_t);
}
extern "C" {
    pub fn rocksdb_env_lower_thread_pool_cpu_priority(env: *mut rocksdb_env_t);
}
extern "C" {
    pub fn rocksdb_env_lower_high_priority_thread_pool_cpu_priority(env: *mut rocksdb_env_t);
}
extern "C" {
    pub fn rocksdb_env_destroy(arg1: *mut rocksdb_env_t);
}
extern "C" {
    pub fn rocksdb_envoptions_create() -> *mut rocksdb_envoptions_t;
}
extern "C" {
    pub fn rocksdb_envoptions_destroy(opt: *mut rocksdb_envoptions_t);
}
extern "C" {
    pub fn rocksdb_create_dir_if_missing(
        env: *mut rocksdb_env_t,
        path: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_create(
        env: *const rocksdb_envoptions_t,
        io_options: *const rocksdb_options_t,
    ) -> *mut rocksdb_sstfilewriter_t;
}
extern "C" {
    pub fn rocksdb_sstfilewriter_create_with_comparator(
        env: *const rocksdb_envoptions_t,
        io_options: *const rocksdb_options_t,
        comparator: *const rocksdb_comparator_t,
    ) -> *mut rocksdb_sstfilewriter_t;
}
extern "C" {
    pub fn rocksdb_sstfilewriter_open(
        writer: *mut rocksdb_sstfilewriter_t,
        name: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_add(
        writer: *mut rocksdb_sstfilewriter_t,
        key: *const libc::c_char,
        keylen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_put(
        writer: *mut rocksdb_sstfilewriter_t,
        key: *const libc::c_char,
        keylen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_put_with_ts(
        writer: *mut rocksdb_sstfilewriter_t,
        key: *const libc::c_char,
        keylen: usize,
        ts: *const libc::c_char,
        tslen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_merge(
        writer: *mut rocksdb_sstfilewriter_t,
        key: *const libc::c_char,
        keylen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_delete(
        writer: *mut rocksdb_sstfilewriter_t,
        key: *const libc::c_char,
        keylen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_delete_with_ts(
        writer: *mut rocksdb_sstfilewriter_t,
        key: *const libc::c_char,
        keylen: usize,
        ts: *const libc::c_char,
        tslen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_delete_range(
        writer: *mut rocksdb_sstfilewriter_t,
        begin_key: *const libc::c_char,
        begin_keylen: usize,
        end_key: *const libc::c_char,
        end_keylen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_finish(
        writer: *mut rocksdb_sstfilewriter_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_file_size(
        writer: *mut rocksdb_sstfilewriter_t,
        file_size: *mut u64,
    );
}
extern "C" {
    pub fn rocksdb_sstfilewriter_destroy(writer: *mut rocksdb_sstfilewriter_t);
}
extern "C" {
    pub fn rocksdb_ingestexternalfileoptions_create() -> *mut rocksdb_ingestexternalfileoptions_t;
}
extern "C" {
    pub fn rocksdb_ingestexternalfileoptions_set_move_files(
        opt: *mut rocksdb_ingestexternalfileoptions_t,
        move_files: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_ingestexternalfileoptions_set_snapshot_consistency(
        opt: *mut rocksdb_ingestexternalfileoptions_t,
        snapshot_consistency: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_ingestexternalfileoptions_set_allow_global_seqno(
        opt: *mut rocksdb_ingestexternalfileoptions_t,
        allow_global_seqno: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_ingestexternalfileoptions_set_allow_blocking_flush(
        opt: *mut rocksdb_ingestexternalfileoptions_t,
        allow_blocking_flush: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_ingestexternalfileoptions_set_ingest_behind(
        opt: *mut rocksdb_ingestexternalfileoptions_t,
        ingest_behind: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_ingestexternalfileoptions_set_fail_if_not_bottommost_level(
        opt: *mut rocksdb_ingestexternalfileoptions_t,
        fail_if_not_bottommost_level: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_ingestexternalfileoptions_destroy(opt: *mut rocksdb_ingestexternalfileoptions_t);
}
extern "C" {
    pub fn rocksdb_ingest_external_file(
        db: *mut rocksdb_t,
        file_list: *const *const libc::c_char,
        list_len: usize,
        opt: *const rocksdb_ingestexternalfileoptions_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_ingest_external_file_cf(
        db: *mut rocksdb_t,
        handle: *mut rocksdb_column_family_handle_t,
        file_list: *const *const libc::c_char,
        list_len: usize,
        opt: *const rocksdb_ingestexternalfileoptions_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_try_catch_up_with_primary(db: *mut rocksdb_t, errptr: *mut *mut libc::c_char);
}
extern "C" {
    pub fn rocksdb_slicetransform_create(
        state: *mut libc::c_void,
        destructor: ::std::option::Option<unsafe extern "C" fn(arg1: *mut libc::c_void)>,
        transform: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                key: *const libc::c_char,
                length: usize,
                dst_length: *mut usize,
            ) -> *mut libc::c_char,
        >,
        in_domain: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                key: *const libc::c_char,
                length: usize,
            ) -> libc::c_uchar,
        >,
        in_range: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut libc::c_void,
                key: *const libc::c_char,
                length: usize,
            ) -> libc::c_uchar,
        >,
        name: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut libc::c_void) -> *const libc::c_char,
        >,
    ) -> *mut rocksdb_slicetransform_t;
}
extern "C" {
    pub fn rocksdb_slicetransform_create_fixed_prefix(arg1: usize)
        -> *mut rocksdb_slicetransform_t;
}
extern "C" {
    pub fn rocksdb_slicetransform_create_noop() -> *mut rocksdb_slicetransform_t;
}
extern "C" {
    pub fn rocksdb_slicetransform_destroy(arg1: *mut rocksdb_slicetransform_t);
}
pub const rocksdb_similar_size_compaction_stop_style: _bindgen_ty_10 = 0;
pub const rocksdb_total_size_compaction_stop_style: _bindgen_ty_10 = 1;
pub type _bindgen_ty_10 = libc::c_uint;
extern "C" {
    pub fn rocksdb_universal_compaction_options_create(
    ) -> *mut rocksdb_universal_compaction_options_t;
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_set_size_ratio(
        arg1: *mut rocksdb_universal_compaction_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_get_size_ratio(
        arg1: *mut rocksdb_universal_compaction_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_set_min_merge_width(
        arg1: *mut rocksdb_universal_compaction_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_get_min_merge_width(
        arg1: *mut rocksdb_universal_compaction_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_set_max_merge_width(
        arg1: *mut rocksdb_universal_compaction_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_get_max_merge_width(
        arg1: *mut rocksdb_universal_compaction_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_set_max_size_amplification_percent(
        arg1: *mut rocksdb_universal_compaction_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_get_max_size_amplification_percent(
        arg1: *mut rocksdb_universal_compaction_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_set_compression_size_percent(
        arg1: *mut rocksdb_universal_compaction_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_get_compression_size_percent(
        arg1: *mut rocksdb_universal_compaction_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_set_stop_style(
        arg1: *mut rocksdb_universal_compaction_options_t,
        arg2: libc::c_int,
    );
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_get_stop_style(
        arg1: *mut rocksdb_universal_compaction_options_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_universal_compaction_options_destroy(
        arg1: *mut rocksdb_universal_compaction_options_t,
    );
}
extern "C" {
    pub fn rocksdb_fifo_compaction_options_create() -> *mut rocksdb_fifo_compaction_options_t;
}
extern "C" {
    pub fn rocksdb_fifo_compaction_options_set_allow_compaction(
        fifo_opts: *mut rocksdb_fifo_compaction_options_t,
        allow_compaction: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_fifo_compaction_options_get_allow_compaction(
        fifo_opts: *mut rocksdb_fifo_compaction_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_fifo_compaction_options_set_max_table_files_size(
        fifo_opts: *mut rocksdb_fifo_compaction_options_t,
        size: u64,
    );
}
extern "C" {
    pub fn rocksdb_fifo_compaction_options_get_max_table_files_size(
        fifo_opts: *mut rocksdb_fifo_compaction_options_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_fifo_compaction_options_destroy(
        fifo_opts: *mut rocksdb_fifo_compaction_options_t,
    );
}
extern "C" {
    pub fn rocksdb_livefiles_count(arg1: *const rocksdb_livefiles_t) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_livefiles_column_family_name(
        arg1: *const rocksdb_livefiles_t,
        index: libc::c_int,
    ) -> *const libc::c_char;
}
extern "C" {
    pub fn rocksdb_livefiles_name(
        arg1: *const rocksdb_livefiles_t,
        index: libc::c_int,
    ) -> *const libc::c_char;
}
extern "C" {
    pub fn rocksdb_livefiles_level(
        arg1: *const rocksdb_livefiles_t,
        index: libc::c_int,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_livefiles_size(arg1: *const rocksdb_livefiles_t, index: libc::c_int) -> usize;
}
extern "C" {
    pub fn rocksdb_livefiles_smallestkey(
        arg1: *const rocksdb_livefiles_t,
        index: libc::c_int,
        size: *mut usize,
    ) -> *const libc::c_char;
}
extern "C" {
    pub fn rocksdb_livefiles_largestkey(
        arg1: *const rocksdb_livefiles_t,
        index: libc::c_int,
        size: *mut usize,
    ) -> *const libc::c_char;
}
extern "C" {
    pub fn rocksdb_livefiles_entries(arg1: *const rocksdb_livefiles_t, index: libc::c_int) -> u64;
}
extern "C" {
    pub fn rocksdb_livefiles_deletions(arg1: *const rocksdb_livefiles_t, index: libc::c_int)
        -> u64;
}
extern "C" {
    pub fn rocksdb_livefiles_destroy(arg1: *const rocksdb_livefiles_t);
}
extern "C" {
    pub fn rocksdb_get_options_from_string(
        base_options: *const rocksdb_options_t,
        opts_str: *const libc::c_char,
        new_options: *mut rocksdb_options_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_delete_file_in_range(
        db: *mut rocksdb_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        limit_key: *const libc::c_char,
        limit_key_len: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_delete_file_in_range_cf(
        db: *mut rocksdb_t,
        column_family: *mut rocksdb_column_family_handle_t,
        start_key: *const libc::c_char,
        start_key_len: usize,
        limit_key: *const libc::c_char,
        limit_key_len: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_get_column_family_metadata(
        db: *mut rocksdb_t,
    ) -> *mut rocksdb_column_family_metadata_t;
}
extern "C" {
    #[doc = " Returns the rocksdb_column_family_metadata_t of the specified\n column family.\n\n Note that the caller is responsible to release the returned memory\n using rocksdb_column_family_metadata_destroy."]
    pub fn rocksdb_get_column_family_metadata_cf(
        db: *mut rocksdb_t,
        column_family: *mut rocksdb_column_family_handle_t,
    ) -> *mut rocksdb_column_family_metadata_t;
}
extern "C" {
    pub fn rocksdb_column_family_metadata_destroy(cf_meta: *mut rocksdb_column_family_metadata_t);
}
extern "C" {
    pub fn rocksdb_column_family_metadata_get_size(
        cf_meta: *mut rocksdb_column_family_metadata_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_column_family_metadata_get_file_count(
        cf_meta: *mut rocksdb_column_family_metadata_t,
    ) -> usize;
}
extern "C" {
    pub fn rocksdb_column_family_metadata_get_name(
        cf_meta: *mut rocksdb_column_family_metadata_t,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_column_family_metadata_get_level_count(
        cf_meta: *mut rocksdb_column_family_metadata_t,
    ) -> usize;
}
extern "C" {
    #[doc = " Returns the rocksdb_level_metadata_t of the ith level from the specified\n column family metadata.\n\n If the specified i is greater than or equal to the number of levels\n in the specified column family, then NULL will be returned.\n\n Note that the caller is responsible to release the returned memory\n using rocksdb_level_metadata_destroy before releasing its parent\n rocksdb_column_family_metadata_t."]
    pub fn rocksdb_column_family_metadata_get_level_metadata(
        cf_meta: *mut rocksdb_column_family_metadata_t,
        i: usize,
    ) -> *mut rocksdb_level_metadata_t;
}
extern "C" {
    #[doc = " Releases the specified rocksdb_level_metadata_t.\n\n Note that the specified rocksdb_level_metadata_t must be released\n before the release of its parent rocksdb_column_family_metadata_t."]
    pub fn rocksdb_level_metadata_destroy(level_meta: *mut rocksdb_level_metadata_t);
}
extern "C" {
    pub fn rocksdb_level_metadata_get_level(
        level_meta: *mut rocksdb_level_metadata_t,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_level_metadata_get_size(level_meta: *mut rocksdb_level_metadata_t) -> u64;
}
extern "C" {
    pub fn rocksdb_level_metadata_get_file_count(
        level_meta: *mut rocksdb_level_metadata_t,
    ) -> usize;
}
extern "C" {
    #[doc = " Returns the sst_file_metadata_t of the ith file from the specified level\n metadata.\n\n If the specified i is greater than or equal to the number of files\n in the specified level, then NULL will be returned.\n\n Note that the caller is responsible to release the returned memory\n using rocksdb_sst_file_metadata_destroy before releasing its\n parent rocksdb_level_metadata_t."]
    pub fn rocksdb_level_metadata_get_sst_file_metadata(
        level_meta: *mut rocksdb_level_metadata_t,
        i: usize,
    ) -> *mut rocksdb_sst_file_metadata_t;
}
extern "C" {
    #[doc = " Releases the specified rocksdb_sst_file_metadata_t.\n\n Note that the specified rocksdb_sst_file_metadata_t must be released\n before the release of its parent rocksdb_level_metadata_t."]
    pub fn rocksdb_sst_file_metadata_destroy(file_meta: *mut rocksdb_sst_file_metadata_t);
}
extern "C" {
    pub fn rocksdb_sst_file_metadata_get_relative_filename(
        file_meta: *mut rocksdb_sst_file_metadata_t,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_sst_file_metadata_get_directory(
        file_meta: *mut rocksdb_sst_file_metadata_t,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_sst_file_metadata_get_size(file_meta: *mut rocksdb_sst_file_metadata_t) -> u64;
}
extern "C" {
    #[doc = " Returns the smallest key of the specified sst file.\n The caller is responsible for releasing the returned memory.\n\n @param file_meta the metadata of an SST file to obtain its smallest key.\n @param len the out value which will contain the length of the returned key\n     after the function call."]
    pub fn rocksdb_sst_file_metadata_get_smallestkey(
        file_meta: *mut rocksdb_sst_file_metadata_t,
        len: *mut usize,
    ) -> *mut libc::c_char;
}
extern "C" {
    #[doc = " Returns the smallest key of the specified sst file.\n The caller is responsible for releasing the returned memory.\n\n @param file_meta the metadata of an SST file to obtain its smallest key.\n @param len the out value which will contain the length of the returned key\n     after the function call."]
    pub fn rocksdb_sst_file_metadata_get_largestkey(
        file_meta: *mut rocksdb_sst_file_metadata_t,
        len: *mut usize,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_transactiondb_create_column_family(
        txn_db: *mut rocksdb_transactiondb_t,
        column_family_options: *const rocksdb_options_t,
        column_family_name: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_column_family_handle_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_open(
        options: *const rocksdb_options_t,
        txn_db_options: *const rocksdb_transactiondb_options_t,
        name: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_transactiondb_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_open_column_families(
        options: *const rocksdb_options_t,
        txn_db_options: *const rocksdb_transactiondb_options_t,
        name: *const libc::c_char,
        num_column_families: libc::c_int,
        column_family_names: *const *const libc::c_char,
        column_family_options: *const *const rocksdb_options_t,
        column_family_handles: *mut *mut rocksdb_column_family_handle_t,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_transactiondb_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_create_snapshot(
        txn_db: *mut rocksdb_transactiondb_t,
    ) -> *const rocksdb_snapshot_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_release_snapshot(
        txn_db: *mut rocksdb_transactiondb_t,
        snapshot: *const rocksdb_snapshot_t,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_property_value(
        db: *mut rocksdb_transactiondb_t,
        propname: *const libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_transactiondb_property_int(
        db: *mut rocksdb_transactiondb_t,
        propname: *const libc::c_char,
        out_val: *mut u64,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_transactiondb_get_base_db(
        txn_db: *mut rocksdb_transactiondb_t,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_close_base_db(base_db: *mut rocksdb_t);
}
extern "C" {
    pub fn rocksdb_transaction_begin(
        txn_db: *mut rocksdb_transactiondb_t,
        write_options: *const rocksdb_writeoptions_t,
        txn_options: *const rocksdb_transaction_options_t,
        old_txn: *mut rocksdb_transaction_t,
    ) -> *mut rocksdb_transaction_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_get_prepared_transactions(
        txn_db: *mut rocksdb_transactiondb_t,
        cnt: *mut usize,
    ) -> *mut *mut rocksdb_transaction_t;
}
extern "C" {
    pub fn rocksdb_transaction_set_name(
        txn: *mut rocksdb_transaction_t,
        name: *const libc::c_char,
        name_len: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_get_name(
        txn: *mut rocksdb_transaction_t,
        name_len: *mut usize,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_transaction_prepare(
        txn: *mut rocksdb_transaction_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_commit(
        txn: *mut rocksdb_transaction_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_rollback(
        txn: *mut rocksdb_transaction_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_set_savepoint(txn: *mut rocksdb_transaction_t);
}
extern "C" {
    pub fn rocksdb_transaction_rollback_to_savepoint(
        txn: *mut rocksdb_transaction_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_destroy(txn: *mut rocksdb_transaction_t);
}
extern "C" {
    pub fn rocksdb_transaction_get_writebatch_wi(
        txn: *mut rocksdb_transaction_t,
    ) -> *mut rocksdb_writebatch_wi_t;
}
extern "C" {
    pub fn rocksdb_transaction_rebuild_from_writebatch(
        txn: *mut rocksdb_transaction_t,
        writebatch: *mut rocksdb_writebatch_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_rebuild_from_writebatch_wi(
        txn: *mut rocksdb_transaction_t,
        wi: *mut rocksdb_writebatch_wi_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_set_commit_timestamp(
        txn: *mut rocksdb_transaction_t,
        commit_timestamp: u64,
    );
}
extern "C" {
    pub fn rocksdb_transaction_set_read_timestamp_for_validation(
        txn: *mut rocksdb_transaction_t,
        read_timestamp: u64,
    );
}
extern "C" {
    pub fn rocksdb_transaction_get_snapshot(
        txn: *mut rocksdb_transaction_t,
    ) -> *const rocksdb_snapshot_t;
}
extern "C" {
    pub fn rocksdb_transaction_get(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        klen: usize,
        vlen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_transaction_get_pinned(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        klen: usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_pinnableslice_t;
}
extern "C" {
    pub fn rocksdb_transaction_get_cf(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        vlen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_transaction_get_pinned_cf(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_pinnableslice_t;
}
extern "C" {
    pub fn rocksdb_transaction_get_for_update(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        klen: usize,
        vlen: *mut usize,
        exclusive: libc::c_uchar,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_transaction_get_pinned_for_update(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        klen: usize,
        exclusive: libc::c_uchar,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_pinnableslice_t;
}
extern "C" {
    pub fn rocksdb_transaction_get_for_update_cf(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        vlen: *mut usize,
        exclusive: libc::c_uchar,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_transaction_get_pinned_for_update_cf(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        exclusive: libc::c_uchar,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_pinnableslice_t;
}
extern "C" {
    pub fn rocksdb_transaction_multi_get(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values_list: *mut *mut libc::c_char,
        values_list_sizes: *mut usize,
        errs: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_multi_get_for_update(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values_list: *mut *mut libc::c_char,
        values_list_sizes: *mut usize,
        errs: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_multi_get_cf(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        column_families: *const *const rocksdb_column_family_handle_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values_list: *mut *mut libc::c_char,
        values_list_sizes: *mut usize,
        errs: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_multi_get_for_update_cf(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        column_families: *const *const rocksdb_column_family_handle_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values_list: *mut *mut libc::c_char,
        values_list_sizes: *mut usize,
        errs: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_get(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        klen: usize,
        vlen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_transactiondb_get_pinned(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        klen: usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_pinnableslice_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_get_cf(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        vallen: *mut usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_transactiondb_get_pinned_cf(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_pinnableslice_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_multi_get(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_readoptions_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values_list: *mut *mut libc::c_char,
        values_list_sizes: *mut usize,
        errs: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_multi_get_cf(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_readoptions_t,
        column_families: *const *const rocksdb_column_family_handle_t,
        num_keys: usize,
        keys_list: *const *const libc::c_char,
        keys_list_sizes: *const usize,
        values_list: *mut *mut libc::c_char,
        values_list_sizes: *mut usize,
        errs: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_put(
        txn: *mut rocksdb_transaction_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_put_cf(
        txn: *mut rocksdb_transaction_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_put(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_writeoptions_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_put_cf(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        val: *const libc::c_char,
        vallen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_write(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_writeoptions_t,
        batch: *mut rocksdb_writebatch_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_merge(
        txn: *mut rocksdb_transaction_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_merge_cf(
        txn: *mut rocksdb_transaction_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_merge(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_writeoptions_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_merge_cf(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        val: *const libc::c_char,
        vlen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_delete(
        txn: *mut rocksdb_transaction_t,
        key: *const libc::c_char,
        klen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_delete_cf(
        txn: *mut rocksdb_transaction_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        klen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_delete(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_writeoptions_t,
        key: *const libc::c_char,
        klen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_delete_cf(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_writeoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transaction_create_iterator(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
    ) -> *mut rocksdb_iterator_t;
}
extern "C" {
    pub fn rocksdb_transaction_create_iterator_cf(
        txn: *mut rocksdb_transaction_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
    ) -> *mut rocksdb_iterator_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_create_iterator(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_readoptions_t,
    ) -> *mut rocksdb_iterator_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_create_iterator_cf(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
    ) -> *mut rocksdb_iterator_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_close(txn_db: *mut rocksdb_transactiondb_t);
}
extern "C" {
    pub fn rocksdb_transactiondb_flush(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_flushoptions_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_flush_cf(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_flushoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_flush_cfs(
        txn_db: *mut rocksdb_transactiondb_t,
        options: *const rocksdb_flushoptions_t,
        column_families: *mut *mut rocksdb_column_family_handle_t,
        num_column_families: libc::c_int,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_flush_wal(
        txn_db: *mut rocksdb_transactiondb_t,
        sync: libc::c_uchar,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_checkpoint_object_create(
        txn_db: *mut rocksdb_transactiondb_t,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_checkpoint_t;
}
extern "C" {
    pub fn rocksdb_optimistictransactiondb_open(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_optimistictransactiondb_t;
}
extern "C" {
    pub fn rocksdb_optimistictransactiondb_open_column_families(
        options: *const rocksdb_options_t,
        name: *const libc::c_char,
        num_column_families: libc::c_int,
        column_family_names: *const *const libc::c_char,
        column_family_options: *const *const rocksdb_options_t,
        column_family_handles: *mut *mut rocksdb_column_family_handle_t,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_optimistictransactiondb_t;
}
extern "C" {
    pub fn rocksdb_optimistictransactiondb_get_base_db(
        otxn_db: *mut rocksdb_optimistictransactiondb_t,
    ) -> *mut rocksdb_t;
}
extern "C" {
    pub fn rocksdb_optimistictransactiondb_close_base_db(base_db: *mut rocksdb_t);
}
extern "C" {
    pub fn rocksdb_optimistictransaction_begin(
        otxn_db: *mut rocksdb_optimistictransactiondb_t,
        write_options: *const rocksdb_writeoptions_t,
        otxn_options: *const rocksdb_optimistictransaction_options_t,
        old_txn: *mut rocksdb_transaction_t,
    ) -> *mut rocksdb_transaction_t;
}
extern "C" {
    pub fn rocksdb_optimistictransactiondb_write(
        otxn_db: *mut rocksdb_optimistictransactiondb_t,
        options: *const rocksdb_writeoptions_t,
        batch: *mut rocksdb_writebatch_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_optimistictransactiondb_close(otxn_db: *mut rocksdb_optimistictransactiondb_t);
}
extern "C" {
    pub fn rocksdb_optimistictransactiondb_checkpoint_object_create(
        otxn_db: *mut rocksdb_optimistictransactiondb_t,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_checkpoint_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_options_create() -> *mut rocksdb_transactiondb_options_t;
}
extern "C" {
    pub fn rocksdb_transactiondb_options_destroy(opt: *mut rocksdb_transactiondb_options_t);
}
extern "C" {
    pub fn rocksdb_transactiondb_options_set_max_num_locks(
        opt: *mut rocksdb_transactiondb_options_t,
        max_num_locks: i64,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_options_set_num_stripes(
        opt: *mut rocksdb_transactiondb_options_t,
        num_stripes: usize,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_options_set_transaction_lock_timeout(
        opt: *mut rocksdb_transactiondb_options_t,
        txn_lock_timeout: i64,
    );
}
extern "C" {
    pub fn rocksdb_transactiondb_options_set_default_lock_timeout(
        opt: *mut rocksdb_transactiondb_options_t,
        default_lock_timeout: i64,
    );
}
extern "C" {
    pub fn rocksdb_transaction_options_create() -> *mut rocksdb_transaction_options_t;
}
extern "C" {
    pub fn rocksdb_transaction_options_destroy(opt: *mut rocksdb_transaction_options_t);
}
extern "C" {
    pub fn rocksdb_transaction_options_set_set_snapshot(
        opt: *mut rocksdb_transaction_options_t,
        v: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_transaction_options_set_deadlock_detect(
        opt: *mut rocksdb_transaction_options_t,
        v: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_transaction_options_set_lock_timeout(
        opt: *mut rocksdb_transaction_options_t,
        lock_timeout: i64,
    );
}
extern "C" {
    pub fn rocksdb_transaction_options_set_expiration(
        opt: *mut rocksdb_transaction_options_t,
        expiration: i64,
    );
}
extern "C" {
    pub fn rocksdb_transaction_options_set_deadlock_detect_depth(
        opt: *mut rocksdb_transaction_options_t,
        depth: i64,
    );
}
extern "C" {
    pub fn rocksdb_transaction_options_set_max_write_batch_size(
        opt: *mut rocksdb_transaction_options_t,
        size: usize,
    );
}
extern "C" {
    pub fn rocksdb_transaction_options_set_skip_prepare(
        opt: *mut rocksdb_transaction_options_t,
        v: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_optimistictransaction_options_create(
    ) -> *mut rocksdb_optimistictransaction_options_t;
}
extern "C" {
    pub fn rocksdb_optimistictransaction_options_destroy(
        opt: *mut rocksdb_optimistictransaction_options_t,
    );
}
extern "C" {
    pub fn rocksdb_optimistictransaction_options_set_set_snapshot(
        opt: *mut rocksdb_optimistictransaction_options_t,
        v: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_optimistictransactiondb_property_value(
        db: *mut rocksdb_optimistictransactiondb_t,
        propname: *const libc::c_char,
    ) -> *mut libc::c_char;
}
extern "C" {
    pub fn rocksdb_optimistictransactiondb_property_int(
        db: *mut rocksdb_optimistictransactiondb_t,
        propname: *const libc::c_char,
        out_val: *mut u64,
    ) -> libc::c_int;
}
extern "C" {
    pub fn rocksdb_free(ptr: *mut libc::c_void);
}
extern "C" {
    pub fn rocksdb_get_pinned(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        key: *const libc::c_char,
        keylen: usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_pinnableslice_t;
}
extern "C" {
    pub fn rocksdb_get_pinned_cf(
        db: *mut rocksdb_t,
        options: *const rocksdb_readoptions_t,
        column_family: *mut rocksdb_column_family_handle_t,
        key: *const libc::c_char,
        keylen: usize,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_pinnableslice_t;
}
extern "C" {
    pub fn rocksdb_pinnableslice_destroy(v: *mut rocksdb_pinnableslice_t);
}
extern "C" {
    pub fn rocksdb_pinnableslice_value(
        t: *const rocksdb_pinnableslice_t,
        vlen: *mut usize,
    ) -> *const libc::c_char;
}
extern "C" {
    pub fn rocksdb_memory_consumers_create() -> *mut rocksdb_memory_consumers_t;
}
extern "C" {
    pub fn rocksdb_memory_consumers_add_db(
        consumers: *mut rocksdb_memory_consumers_t,
        db: *mut rocksdb_t,
    );
}
extern "C" {
    pub fn rocksdb_memory_consumers_add_cache(
        consumers: *mut rocksdb_memory_consumers_t,
        cache: *mut rocksdb_cache_t,
    );
}
extern "C" {
    pub fn rocksdb_memory_consumers_destroy(consumers: *mut rocksdb_memory_consumers_t);
}
extern "C" {
    pub fn rocksdb_approximate_memory_usage_create(
        consumers: *mut rocksdb_memory_consumers_t,
        errptr: *mut *mut libc::c_char,
    ) -> *mut rocksdb_memory_usage_t;
}
extern "C" {
    pub fn rocksdb_approximate_memory_usage_destroy(usage: *mut rocksdb_memory_usage_t);
}
extern "C" {
    pub fn rocksdb_approximate_memory_usage_get_mem_table_total(
        memory_usage: *mut rocksdb_memory_usage_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_approximate_memory_usage_get_mem_table_unflushed(
        memory_usage: *mut rocksdb_memory_usage_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_approximate_memory_usage_get_mem_table_readers_total(
        memory_usage: *mut rocksdb_memory_usage_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_approximate_memory_usage_get_cache_total(
        memory_usage: *mut rocksdb_memory_usage_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_options_set_dump_malloc_stats(arg1: *mut rocksdb_options_t, arg2: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_options_set_memtable_whole_key_filtering(
        arg1: *mut rocksdb_options_t,
        arg2: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_cancel_all_background_work(db: *mut rocksdb_t, wait: libc::c_uchar);
}
extern "C" {
    pub fn rocksdb_disable_manual_compaction(db: *mut rocksdb_t);
}
extern "C" {
    pub fn rocksdb_enable_manual_compaction(db: *mut rocksdb_t);
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_create() -> *mut rocksdb_statistics_histogram_data_t;
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_destroy(
        data: *mut rocksdb_statistics_histogram_data_t,
    );
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_get_median(
        data: *mut rocksdb_statistics_histogram_data_t,
    ) -> f64;
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_get_p95(
        data: *mut rocksdb_statistics_histogram_data_t,
    ) -> f64;
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_get_p99(
        data: *mut rocksdb_statistics_histogram_data_t,
    ) -> f64;
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_get_average(
        data: *mut rocksdb_statistics_histogram_data_t,
    ) -> f64;
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_get_std_dev(
        data: *mut rocksdb_statistics_histogram_data_t,
    ) -> f64;
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_get_max(
        data: *mut rocksdb_statistics_histogram_data_t,
    ) -> f64;
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_get_count(
        data: *mut rocksdb_statistics_histogram_data_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_get_sum(
        data: *mut rocksdb_statistics_histogram_data_t,
    ) -> u64;
}
extern "C" {
    pub fn rocksdb_statistics_histogram_data_get_min(
        data: *mut rocksdb_statistics_histogram_data_t,
    ) -> f64;
}
extern "C" {
    pub fn rocksdb_wait_for_compact(
        db: *mut rocksdb_t,
        options: *mut rocksdb_wait_for_compact_options_t,
        errptr: *mut *mut libc::c_char,
    );
}
extern "C" {
    pub fn rocksdb_wait_for_compact_options_create() -> *mut rocksdb_wait_for_compact_options_t;
}
extern "C" {
    pub fn rocksdb_wait_for_compact_options_destroy(opt: *mut rocksdb_wait_for_compact_options_t);
}
extern "C" {
    pub fn rocksdb_wait_for_compact_options_set_abort_on_pause(
        opt: *mut rocksdb_wait_for_compact_options_t,
        v: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_wait_for_compact_options_get_abort_on_pause(
        opt: *mut rocksdb_wait_for_compact_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_wait_for_compact_options_set_flush(
        opt: *mut rocksdb_wait_for_compact_options_t,
        v: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_wait_for_compact_options_get_flush(
        opt: *mut rocksdb_wait_for_compact_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_wait_for_compact_options_set_close_db(
        opt: *mut rocksdb_wait_for_compact_options_t,
        v: libc::c_uchar,
    );
}
extern "C" {
    pub fn rocksdb_wait_for_compact_options_get_close_db(
        opt: *mut rocksdb_wait_for_compact_options_t,
    ) -> libc::c_uchar;
}
extern "C" {
    pub fn rocksdb_wait_for_compact_options_set_timeout(
        opt: *mut rocksdb_wait_for_compact_options_t,
        microseconds: u64,
    );
}
extern "C" {
    pub fn rocksdb_wait_for_compact_options_get_timeout(
        opt: *mut rocksdb_wait_for_compact_options_t,
    ) -> u64;
}
pub type __builtin_va_list = *mut libc::c_char;
