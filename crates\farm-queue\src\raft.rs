use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};

use libp2p::PeerId;
use raft::prelude::*;
use raft::{Config, RawNode};
use raft_proto::prelude::*;
use prost::Message as ProstMessage;
use log::{info, warn, error, debug};

use crate::storage::DataStore;
use crate::types::Task;

// Raft 节点状态包装
pub struct RaftNode {
    raw_node: RawNode<MemStorage>,
    peers: HashMap<u64, PeerId>,
    peer_id_to_node_id: HashMap<PeerId, u64>,
    proposals: HashMap<u64, Vec<u8>>,
    last_applied: u64,
    node_id: u64,
    peer_id: PeerId,
}

impl RaftNode {
    pub fn new(
        node_id: u64,
        peer_id: PeerId,
        peers: Vec<PeerId>,
        storage: MemStorage,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let mut cfg = Config {
            id: node_id,
            election_tick: 10,
            heartbeat_tick: 3,
            max_size_per_msg: 1024 * 1024,
            max_inflight_msgs: 256,
            ..Default::default()
        };

        // 设置初始节点列表
        let mut peer_map = HashMap::new();
        let mut peer_id_to_node_id = HashMap::new();
        
        // 添加自己
        peer_map.insert(node_id, peer_id);
        peer_id_to_node_id.insert(peer_id, node_id);
        
        // 添加其他节点
        for (i, p) in peers.iter().enumerate() {
            let id = (i + 2) as u64; // 从2开始，1是自己
            peer_map.insert(id, *p);
            peer_id_to_node_id.insert(*p, id);
        }

        let voters = peer_map.keys().cloned().collect::<Vec<_>>();
        cfg.voters = voters;

        let raw_node = RawNode::new(&cfg, storage)?;

        Ok(RaftNode {
            raw_node,
            peers: peer_map,
            peer_id_to_node_id,
            proposals: HashMap::new(),
            last_applied: 0,
            node_id,
            peer_id,
        })
    }

    pub fn tick(&mut self) {
        self.raw_node.tick();
    }

    pub fn propose(&mut self, data: Vec<u8>) -> Result<(), Box<dyn std::error::Error>> {
        let proposal_id = rand::random::<u64>();
        self.proposals.insert(proposal_id, data.clone());
        self.raw_node.propose(vec![], data)?;
        Ok(())
    }

    pub fn step(&mut self, msg: Message) -> Result<(), Box<dyn std::error::Error>> {
        self.raw_node.step(msg)?;
        Ok(())
    }

    pub fn ready(&mut self) -> Option<Ready> {
        if !self.raw_node.has_ready() {
            return None;
        }
        Some(self.raw_node.ready())
    }

    pub fn advance(&mut self, ready: Ready) {
        // 处理已提交的条目
        for entry in ready.committed_entries.iter() {
            if entry.data.is_empty() {
                continue;
            }
            
            if entry.index <= self.last_applied {
                continue;
            }
            
            self.last_applied = entry.index;
            
            // 这里应该应用条目到状态机
            debug!("Applied entry at index {}", entry.index);
        }

        self.raw_node.advance(ready);
    }

    pub fn is_leader(&self) -> bool {
        self.raw_node.raft.state == StateRole::Leader
    }

    pub fn get_leader_id(&self) -> Option<u64> {
        let leader_id = self.raw_node.raft.leader_id;
        if leader_id == 0 {
            None
        } else {
            Some(leader_id)
        }
    }

    pub fn get_peer_id(&self, node_id: u64) -> Option<PeerId> {
        self.peers.get(&node_id).copied()
    }

    pub fn get_node_id(&self, peer_id: &PeerId) -> Option<u64> {
        self.peer_id_to_node_id.get(peer_id).copied()
    }

    pub fn campaign(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let msg = Message {
            msg_type: MessageType::MsgHup,
            ..Default::default()
        };
        self.step(msg)?;
        Ok(())
    }
}

// 用于替代原来的 RaftState
pub struct RaftManager {
    node: Arc<Mutex<RaftNode>>,
    storage: Arc<MemStorage>,
    data_store: Arc<DataStore>,
    last_tick_time: Instant,
    tick_interval: Duration,
}

impl RaftManager {
    pub fn new(
        peer_id: PeerId,
        peers: Vec<PeerId>,
        data_store: Arc<DataStore>,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // 生成节点ID（基于PeerId的哈希）
        let node_id = Self::peer_id_to_node_id(&peer_id);
        
        // 创建存储
        let storage = MemStorage::new();
        
        // 从磁盘恢复状态
        if let Ok((term, voted_for)) = data_store.load_raft_state() {
            let mut hs = HardState::default();
            hs.term = term;
            if let Some(vf) = voted_for {
                hs.vote = Self::peer_id_to_node_id(&vf);
            }
            hs.commit = 0;
            storage.wl().set_hardstate(hs);
        }

        let node = RaftNode::new(node_id, peer_id, peers, storage.clone())?;

        Ok(RaftManager {
            node: Arc::new(Mutex::new(node)),
            storage: Arc::new(storage),
            data_store,
            last_tick_time: Instant::now(),
            tick_interval: Duration::from_millis(100),
        })
    }

    fn peer_id_to_node_id(peer_id: &PeerId) -> u64 {
        let bytes = peer_id.to_bytes();
        let mut node_id = 0u64;
        for (i, &b) in bytes.iter().take(8).enumerate() {
            node_id |= (b as u64) << (i * 8);
        }
        node_id
    }

    pub fn tick(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if self.last_tick_time.elapsed() >= self.tick_interval {
            self.node.lock().unwrap().tick();
            self.last_tick_time = Instant::now();
        }
        Ok(())
    }

    pub fn step(&self, msg: Message) -> Result<(), Box<dyn std::error::Error>> {
        self.node.lock().unwrap().step(msg)?;
        Ok(())
    }

    pub fn propose(&self, data: Vec<u8>) -> Result<(), Box<dyn std::error::Error>> {
        self.node.lock().unwrap().propose(data)?;
        Ok(())
    }

    pub fn process_ready(&self) -> Result<Vec<(u64, Vec<Message>)>, Box<dyn std::error::Error>> {
        let mut node = self.node.lock().unwrap();
        
        if let Some(ready) = node.ready() {
            let mut messages_to_send = Vec::new();
            
            // 保存硬状态
            if let Some(hs) = ready.hs() {
                self.storage.wl().set_hardstate(hs.clone());
                
                // 同步到磁盘
                let voted_for = if hs.vote == 0 {
                    None
                } else {
                    node.get_peer_id(hs.vote)
                };
                self.data_store.save_raft_state(hs.term, voted_for)?;
            }

            // 保存条目
            if !ready.entries.is_empty() {
                self.storage.wl().append(&ready.entries)?;
            }

            // 收集要发送的消息
            for msg in ready.messages.iter() {
                if let Some(peer_id) = node.get_peer_id(msg.to) {
                    messages_to_send.push((msg.to, vec![msg.clone()]));
                }
            }

            // 应用快照
            if let Some(snapshot) = ready.snapshot() {
                self.storage.wl().apply_snapshot(snapshot.clone())?;
            }

            // 推进状态
            node.advance(ready);
            
            Ok(messages_to_send)
        } else {
            Ok(Vec::new())
        }
    }

    pub fn is_leader(&self) -> bool {
        self.node.lock().unwrap().is_leader()
    }

    pub fn get_leader_peer_id(&self) -> Option<PeerId> {
        let node = self.node.lock().unwrap();
        node.get_leader_id().and_then(|id| node.get_peer_id(id))
    }

    pub fn campaign(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.node.lock().unwrap().campaign()?;
        Ok(())
    }

    pub fn add_node(&self, peer_id: PeerId) -> Result<(), Box<dyn std::error::Error>> {
        let node_id = Self::peer_id_to_node_id(&peer_id);
        let mut node = self.node.lock().unwrap();
        
        // 构建配置更改
        let mut cc = ConfChange::default();
        cc.node_id = node_id;
        cc.change_type = ConfChangeType::AddNode;
        
        // 序列化配置更改
        let mut buf = Vec::new();
        cc.encode(&mut buf)?;
        
        // 提议配置更改
        node.propose(buf)?;
        
        Ok(())
    }

    pub fn remove_node(&self, peer_id: PeerId) -> Result<(), Box<dyn std::error::Error>> {
        let node_id = Self::peer_id_to_node_id(&peer_id);
        let mut node = self.node.lock().unwrap();
        
        // 构建配置更改
        let mut cc = ConfChange::default();
        cc.node_id = node_id;
        cc.change_type = ConfChangeType::RemoveNode;
        
        // 序列化配置更改
        let mut buf = Vec::new();
        cc.encode(&mut buf)?;
        
        // 提议配置更改
        node.propose(buf)?;
        
        Ok(())
    }

    pub fn handle_conf_change(&self, cc: ConfChange) -> Result<(), Box<dyn std::error::Error>> {
        let mut node = self.node.lock().unwrap();
        node.raw_node.apply_conf_change(&cc)?;
        Ok(())
    }
}

// 为了兼容原有代码，提供一些辅助结构
#[derive(Debug, Clone, PartialEq)]
pub enum NodeState {
    Follower,
    Candidate,
    Leader,
}

// 简化的状态结构，用于兼容原代码
pub struct RaftState {
    pub current_term: u64,
    pub voted_for: Option<PeerId>,
    pub state: NodeState,
    pub leader_id: Option<PeerId>,
    pub last_heartbeat: Instant,
    pub election_timeout: Duration,
    raft_manager: Option<Arc<RaftManager>>,
}

impl RaftState {
    pub fn new() -> Self {
        let mut rng = rand::thread_rng();
        use rand::Rng;
        let election_timeout = Duration::from_millis(150 + rng.gen::<u64>() % 150);
        
        Self {
            current_term: 0,
            voted_for: None,
            state: NodeState::Follower,
            leader_id: None,
            last_heartbeat: Instant::now(),
            election_timeout,
            raft_manager: None,
        }
    }

    pub fn set_raft_manager(&mut self, manager: Arc<RaftManager>) {
        self.raft_manager = Some(manager);
        self.update_from_raft_manager();
    }

    pub fn update_from_raft_manager(&mut self) {
        if let Some(ref manager) = self.raft_manager {
            let node = manager.node.lock().unwrap();
            let raft = &node.raw_node.raft;
            
            self.current_term = raft.term;
            self.voted_for = node.get_peer_id(raft.vote);
            self.leader_id = node.get_leader_id().and_then(|id| node.get_peer_id(id));
            
            self.state = match raft.state {
                StateRole::Follower => NodeState::Follower,
                StateRole::Candidate | StateRole::PreCandidate => NodeState::Candidate,
                StateRole::Leader => NodeState::Leader,
            };
        }
    }

    pub fn reset_election_timer(&mut self) {
        self.last_heartbeat = Instant::now();
        let mut rng = rand::thread_rng();
        use rand::Rng;
        self.election_timeout = Duration::from_millis(150 + rng.gen::<u64>() % 150);
    }

    pub fn is_election_timeout(&self) -> bool {
        Instant::now().duration_since(self.last_heartbeat) > self.election_timeout
    }

    pub fn become_follower(&mut self, term: u64, leader_id: Option<PeerId>) {
        self.state = NodeState::Follower;
        self.current_term = term;
        self.voted_for = None;
        self.leader_id = leader_id;
        self.reset_election_timer();
        
        if let Some(leader) = leader_id {
            info!("成为Follower，任期: {}，Leader: {}", term, leader);
        } else {
            info!("成为Follower，任期: {}", term);
        }
    }

    pub fn become_candidate(&mut self, _self_id: PeerId) {
        self.state = NodeState::Candidate;
        self.reset_election_timer();
        info!("成为候选人，任期: {}", self.current_term);
    }

    pub fn become_leader(&mut self, self_id: PeerId) {
        self.current_term += 1;
        self.state = NodeState::Leader;
        self.leader_id = Some(self_id);
        self.reset_election_timer();
        info!("成为Leader，任期: {}", self.current_term);
    }

    pub fn become_leader_without_term_increment(&mut self, self_id: PeerId) {
        self.state = NodeState::Leader;
        self.leader_id = Some(self_id);
        self.reset_election_timer();
        info!("成为Leader（单节点），保持任期: {}", self.current_term);
    }
}