use rkyv::{Archive, Deserialize as RkyvDeserialize, Serialize as RkyvSerialize};
use libp2p::PeerId;
use std::collections::HashSet;
use std::time::Duration;
use rand::Rng;

#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum NodeState {
    Follower,
    Candidate,
    Leader,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct VoteRequest {
    pub term: u64,
    pub candidate_id: Vec<u8>, // Changed from PeerId to Vec<u8>
    pub last_log_index: u64,
    pub last_log_term: u64,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct VoteResponse {
    pub term: u64,
    pub vote_granted: bool,
    pub voter_id: Vec<u8>, // Changed from PeerId to Vec<u8>
}

#[derive(Debug, <PERSON>lone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct AppendEntries {
    pub term: u64,
    pub leader_id: Vec<u8>, // Changed from PeerId to Vec<u8>
    pub prev_log_index: u64,
    pub prev_log_term: u64,
    pub entries: Vec<LogEntry>,
    pub leader_commit: u64,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct LogEntry {
    pub term: u64,
    pub index: u64,
    pub command: LogCommand,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub enum LogCommand {
    AssignTask { task_id: [u8; 16], worker: Vec<u8> },
    UpdateTaskStatus { task_id: [u8; 16], status: crate::types::TaskStatus },
}

pub struct RaftState {
    pub current_term: u64,
    pub voted_for: Option<PeerId>,
    pub state: NodeState,
    pub leader_id: Option<PeerId>,
    pub votes_received: HashSet<PeerId>,
    pub last_heartbeat: std::time::Instant,
    pub election_timeout: Duration,
    pub log: Vec<LogEntry>,
    pub commit_index: u64,
    pub last_applied: u64,
    pub failed_election_count: u32,
    pub last_election_attempt: Option<std::time::Instant>,
}

impl RaftState {
    pub fn new() -> Self {
        let mut rng = rand::thread_rng();
        let election_timeout = Duration::from_millis(150 + rng.r#gen::<u64>() % 150);
        
        Self {
            current_term: 0,
            voted_for: None,
            state: NodeState::Follower,
            leader_id: None,
            votes_received: HashSet::new(),
            last_heartbeat: std::time::Instant::now(),
            election_timeout,
            log: Vec::new(),
            commit_index: 0,
            last_applied: 0,
            failed_election_count: 0,
            last_election_attempt: None,
        }
    }
    
    pub fn reset_election_timer(&mut self) {
        self.last_heartbeat = std::time::Instant::now();
        let mut rng = rand::thread_rng();
        self.election_timeout = Duration::from_millis(150 + rng.r#gen::<u64>() % 150);
    }
    
    pub fn is_election_timeout(&self) -> bool {
        std::time::Instant::now().duration_since(self.last_heartbeat) > self.election_timeout
    }
    
    pub fn can_start_election(&self, peer_count: usize) -> bool {
        // 单节点总是可以成为Leader
        if peer_count == 0 {
            return true;
        }
        
        // 多节点集群需要检查是否有足够节点形成多数
        let total_nodes = peer_count + 1;
        total_nodes >= 2 // 至少需要2个节点才能进行有意义的选举
    }
    
    pub fn should_become_leader_immediately(&self, connected_peers: usize, subscribed_peers: usize) -> bool {
        // 如果没有其他连接或订阅的节点，立即成为Leader
        connected_peers == 0 && subscribed_peers == 0
    }
    
    pub fn become_candidate(&mut self, self_id: PeerId) {
        // 成为候选人时不增加任期
        self.state = NodeState::Candidate;
        self.voted_for = Some(self_id);
        self.votes_received.clear();
        self.votes_received.insert(self_id);
        self.reset_election_timer();
        self.last_election_attempt = Some(std::time::Instant::now());
        log::info!("成为候选人，保持任期: {}", self.current_term);
    }
    
    pub fn start_election_with_peers(&mut self, self_id: PeerId, has_peers: bool) {
        self.state = NodeState::Candidate;
        
        // 只有在有其他节点时才增加任期
        if has_peers {
            self.current_term += 1;
            log::info!("开始选举，增加任期到: {}", self.current_term);
        } else {
            log::info!("单节点场景，保持任期: {}", self.current_term);
        }
        
        self.voted_for = Some(self_id);
        self.votes_received.clear();
        self.votes_received.insert(self_id);
        self.reset_election_timer();
        self.last_election_attempt = Some(std::time::Instant::now());
    }
    
    pub fn become_leader(&mut self, self_id: PeerId) {
        // 成为Leader时才增加任期
        self.current_term += 1;
        self.state = NodeState::Leader;
        self.leader_id = Some(self_id);
        self.reset_election_timer();
        self.failed_election_count = 0;
        self.last_election_attempt = None;
        log::info!("成为Leader，增加任期到: {}", self.current_term);
    }
    
    pub fn become_leader_without_term_increment(&mut self, self_id: PeerId) {
        // 单节点场景下成为Leader，不增加任期
        self.state = NodeState::Leader;
        self.leader_id = Some(self_id);
        self.reset_election_timer();
        self.failed_election_count = 0;
        self.last_election_attempt = None;
        log::info!("成为Leader，保持任期: {}", self.current_term);
    }
    
    pub fn become_follower(&mut self, term: u64, leader_id: Option<PeerId>) {
        self.state = NodeState::Follower;
        self.current_term = term;
        self.voted_for = None;
        self.leader_id = leader_id;
        self.votes_received.clear();
        self.reset_election_timer();
        
        // Reset failed election count when becoming follower due to higher term
        if leader_id.is_some() {
            self.failed_election_count = 0;
            self.last_election_attempt = None;
        }
        
        if let Some(leader) = leader_id {
            log::info!("成为Follower，任期: {}，Leader: {}", term, leader);
        } else {
            log::info!("成为Follower，任期: {}", term);
        }
    }
    
    pub fn election_failed(&mut self) {
        self.state = NodeState::Follower;
        self.voted_for = None;
        self.votes_received.clear();
        self.failed_election_count += 1;
        self.reset_election_timer();
        
        log::warn!("选举失败，失败次数: {}", self.failed_election_count);
    }
}
