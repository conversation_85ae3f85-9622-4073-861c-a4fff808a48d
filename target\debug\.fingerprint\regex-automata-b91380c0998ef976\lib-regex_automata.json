{"rustc": 14799578172954012516, "features": "[\"alloc\", \"meta\", \"nfa-pikevm\", \"nfa-thompson\", \"std\", \"syntax\", \"unicode-bool\", \"unicode-perl\", \"unicode-word-boundary\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 2225463790103693989, "path": 1008957836664530277, "deps": [[9408802513701742484, "regex_syntax", false, 2447430663303605856]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-automata-b91380c0998ef976\\dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}