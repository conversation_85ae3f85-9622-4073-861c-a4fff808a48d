{"rustc": 14799578172954012516, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"auto-initialize\", \"chrono\", \"default\", \"either\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"unindent\"]", "target": 11844115677734887287, "profile": 4317675006741846053, "path": 11413021106397427124, "deps": [[629381703529241162, "indoc", false, 9369263008880430002], [2828590642173593838, "cfg_if", false, 12229401028168996322], [3958489542916937055, "portable_atomic", false, 6351653176160715757], [4495526598637097934, "parking_lot", false, 11574689243572852607], [4684437522915235464, "libc", false, 10805704521223867303], [7614379793313454108, "pyo3_ffi", false, 7823132849266396421], [12478333222061076338, "pyo3_macros", false, 16802534700106180663], [14643204177830147187, "memoffset", false, 10926427278673050319], [14748792705540276325, "unindent", false, 9572182020151639819], [17396333227350152061, "build_script_build", false, 18157144243248053768]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-c88f538cf9e10341\\dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}