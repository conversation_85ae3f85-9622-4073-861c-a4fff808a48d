use mimalloc::MiMalloc;

#[global_allocator]
static GLOBAL: MiMalloc = MiMalloc;

pub mod types;
pub mod paxos;  // Replaced raft with paxos
pub mod messages;
pub mod storage;
pub mod network;
pub mod queue;
pub mod client;

pub mod logging;
pub use logging::{init_logging, LogConfig};

pub use paxos::*;  // Export paxos instead of raft
pub use messages::*;
pub use storage::*;
pub use network::*;
pub use queue::*;
pub use client::*;