{"rustc": 14799578172954012516, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11057642215492122650, "build_script_build", false, 2422653721894605641]], "local": [{"RerunIfEnvChanged": {"var": "PYO3_CONFIG_FILE", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_NO_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_ENVIRONMENT_SIGNATURE", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "VIRTUAL_ENV", "val": null}}, {"RerunIfEnvChanged": {"var": "CONDA_PREFIX", "val": null}}, {"RerunIfEnvChanged": {"var": "PATH", "val": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Scripts;C:\\msys64\\clang64\\bin;C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler;C:\\Qt\\6.8.1\\msvc2022_64\\bin;C:\\Program Files\\ImageMagick-7.1.1-Q16-HDRI;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.4\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.4\\libnvvp;c:\\windows\\system32;c:\\windows;c:\\windows\\system32\\wbem;c:\\windows\\system32\\windowspowershell\\v1.0\\;c:\\windows\\system32\\openssh\\;c:\\program files\\dotnet\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2024.1.0\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Software\\vcpkg;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\gnuplot;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\CMake\\bin;C:\\protoc\\bin;C:\\Program Files\\NASM;C:\\flutter\\bin;C:\\Program Files\\nodejs\\;C:\\Program Files\\xmake;C:\\Program Files\\Common Files\\Autodesk Shared\\Modules\\Maya;C:\\Program Files\\Autodesk\\Maya2024\\bin;C:\\Program Files\\Yasb\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\Program Files\\gsudo\\Current;D:\\vcpkg;C:\\msys64\\clang64\\bin;C:\\Users\\<USER>\\.cargo\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Program Files\\JetBrains\\CLion 2024.2.3\\bin;;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;c:\\users\\<USER>\\appdata\\roaming\\python\\scripts;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\.rustup\\toolchains\\nightly-x86_64-pc-windows-msvc\\bin"}}, {"RerunIfEnvChanged": {"var": "PYO3_USE_ABI3_FORWARD_COMPATIBILITY", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}