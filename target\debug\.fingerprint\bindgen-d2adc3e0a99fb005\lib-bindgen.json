{"rustc": 14799578172954012516, "features": "[\"runtime\"]", "declared_features": "[\"__cli\", \"__testing_only_extra_assertions\", \"__testing_only_libclang_16\", \"__testing_only_libclang_9\", \"default\", \"experimental\", \"logging\", \"prettyplease\", \"runtime\", \"static\", \"which-rustfmt\"]", "target": 15460903241111225995, "profile": 2225463790103693989, "path": 6323201182297519366, "deps": [[950716570147248582, "cexpr", false, 18242477602941054656], [2004958070545769120, "lazycell", false, 17500628159421456337], [3060637413840920116, "proc_macro2", false, 17861231915632534772], [4885725550624711673, "clang_sys", false, 9936355224651141020], [4974441333307933176, "syn", false, 5125971225996987035], [7896293946984509699, "bitflags", false, 12224930908667793411], [8410525223747752176, "shlex", false, 11857247705523502006], [9451456094439810778, "regex", false, 10664981519166830513], [9907446631595856440, "build_script_build", false, 12816516030267171003], [14931062873021150766, "itertools", false, 11455770226309199386], [16055916053474393816, "rustc_hash", false, 2581841000980770044], [17917672826516349275, "lazy_static", false, 8078700589956690812], [17990358020177143287, "quote", false, 16547450557769863501]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bindgen-d2adc3e0a99fb005\\dep-lib-bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}