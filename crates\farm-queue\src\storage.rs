use std::{error::Error, time::Duration, path::PathBuf, sync::Arc, convert::TryInto};
use libp2p::PeerId;
use crate::types::{Task, TaskStatus};
use rocksdb::{DB, Options, ColumnFamily, ColumnFamilyDescriptor, WriteBatch, IteratorMode};
use log::{warn, info, debug};
use std::time::{SystemTime, UNIX_EPOCH};
use anyhow::Result;
use prost::Message;
use crate::messages::proto;
use crate::messages::{convert_task_to_proto, convert_task_from_proto};

#[derive(Debug, Clone)]
pub struct RocksDbConfig {
    pub database_path: PathBuf,
    pub max_open_files: i32,
    pub write_buffer_size: usize,
    pub max_write_buffer_number: i32,
    pub target_file_size_base: u64,
    pub max_background_jobs: i32,
    pub cache_size: usize,
    pub max_retries: u32,
    pub retry_delay: Duration,
    pub enable_statistics: bool,
}

impl Default for RocksDbConfig {
    fn default() -> Self {
        Self {
            database_path: PathBuf::from("./data"),
            max_open_files: 1000,
            write_buffer_size: 64 * 1024 * 1024, // 64MB
            max_write_buffer_number: 3,
            target_file_size_base: 64 * 1024 * 1024, // 64MB
            max_background_jobs: 4,
            cache_size: 128 * 1024 * 1024, // 128MB
            max_retries: 3,
            retry_delay: Duration::from_millis(100),
            enable_statistics: false,
        }
    }
}

#[derive(Debug, Clone)]
pub struct CleanupConfig {
    pub enable_auto_cleanup: bool,
    pub cleanup_interval: Duration,
    pub max_task_age_days: u32,
    pub max_completed_tasks: usize,
    pub max_failed_tasks: usize,
    pub compact_after_cleanup: bool,
}

impl Default for CleanupConfig {
    fn default() -> Self {
        Self {
            enable_auto_cleanup: true,
            cleanup_interval: Duration::from_secs(3600), // 1小时
            max_task_age_days: 30, // 保留30天
            max_completed_tasks: 10000, // 最多保留1万个已完成任务
            max_failed_tasks: 5000, // 最多保留5千个失败任务
            compact_after_cleanup: true,
        }
    }
}

#[derive(Debug)]
pub enum StorageError {
    RocksDbError(rocksdb::Error),
    SerializationError(String),
    ConflictError { task_id: uuid::Uuid, expected_version: u64, actual_version: u64 },
    NotFound(uuid::Uuid),
    InvalidData(String),
    IoError(std::io::Error),
}

impl std::fmt::Display for StorageError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            StorageError::RocksDbError(e) => write!(f, "RocksDB error: {}", e),
            StorageError::SerializationError(e) => write!(f, "Serialization error: {}", e),
            StorageError::ConflictError { task_id, expected_version, actual_version } => {
                write!(f, "Version conflict for task {}: expected {}, got {}", 
                       task_id, expected_version, actual_version)
            }
            StorageError::NotFound(id) => write!(f, "Task not found: {}", id),
            StorageError::InvalidData(msg) => write!(f, "Invalid data: {}", msg),
            StorageError::IoError(e) => write!(f, "IO error: {}", e),
        }
    }
}

impl Error for StorageError {}

impl From<rocksdb::Error> for StorageError {
    fn from(err: rocksdb::Error) -> Self {
        StorageError::RocksDbError(err)
    }
}

impl From<std::io::Error> for StorageError {
    fn from(err: std::io::Error) -> Self {
        StorageError::IoError(err)
    }
}

const TASKS_CF: &str = "tasks";
const RAFT_STATE_CF: &str = "raft_state";
const TASK_VERSIONS_CF: &str = "task_versions";
const TASK_INDEX_CF: &str = "task_index";

pub struct DataStore {
    db: Arc<DB>,
    config: RocksDbConfig,
}

impl DataStore {
    pub fn new(config: RocksDbConfig) -> anyhow::Result<Self> {
        std::fs::create_dir_all(&config.database_path)?;
        
        let mut db_opts = Options::default();
        db_opts.create_if_missing(true);
        db_opts.create_missing_column_families(true);
        db_opts.set_max_open_files(config.max_open_files);
        db_opts.set_write_buffer_size(config.write_buffer_size);
        db_opts.set_max_write_buffer_number(config.max_write_buffer_number);
        db_opts.set_target_file_size_base(config.target_file_size_base);
        db_opts.set_max_background_jobs(config.max_background_jobs);
        
        if config.enable_statistics {
            db_opts.enable_statistics();
        }
        
        // Configure block-based table options for better performance
        let mut block_opts = rocksdb::BlockBasedOptions::default();
        block_opts.set_block_cache(&rocksdb::Cache::new_lru_cache(config.cache_size));
        block_opts.set_bloom_filter(10.0, false);
        db_opts.set_block_based_table_factory(&block_opts);
        
        // Column family descriptors
        let cfs = vec![
            ColumnFamilyDescriptor::new(TASKS_CF, Options::default()),
            ColumnFamilyDescriptor::new(RAFT_STATE_CF, Options::default()),
            ColumnFamilyDescriptor::new(TASK_VERSIONS_CF, Options::default()),
            ColumnFamilyDescriptor::new(TASK_INDEX_CF, Options::default()),
        ];
        
        let db = DB::open_cf_descriptors(&db_opts, &config.database_path, cfs)?;
        
        info!("RocksDB initialized at: {:?}", config.database_path);
        
        Ok(Self {
            db: Arc::new(db),
            config,
        })
    }
    
    pub fn save_task(&self, task: &Task) -> Result<()> {
        self.save_task_with_retry(task, 0)
    }
    
    fn save_task_with_retry(&self, task: &Task, retry_count: u32) -> Result<()> {
        if retry_count >= self.config.max_retries {
            return Err(anyhow::anyhow!("Max retries exceeded after {} attempts", self.config.max_retries));
        }
        
        let task_id = uuid::Uuid::from_bytes(task.id);
        let task_key = format!("task:{}", task_id);
        let version_key = format!("version:{}", task_id);
        let index_key = format!("index:{}:{:020}:{}", 
                               task.status.status_string(), 
                               task.updated_at, 
                               task_id);
        
        // Check for version conflicts
        if let Ok(Some(existing_version_data)) = self.db.get_cf(
            self.db.cf_handle(TASK_VERSIONS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_VERSIONS_CF))?,
            &version_key
        ) {
            let existing_version = u64::from_le_bytes(
                existing_version_data.as_slice().try_into()
                    .map_err(|_| StorageError::InvalidData("Invalid version data".to_string()))?
            );
            
            if existing_version > task.version {
                return Err(StorageError::ConflictError {
                    task_id,
                    expected_version: task.version,
                    actual_version: existing_version,
                }.into());
            }
        }
        
        match self.save_task_internal(task, &task_key, &version_key, &index_key) {
            Ok(_) => {
                debug!("Successfully saved task: {}", task_id);
                Ok(())
            }
            Err(e) => {
                warn!("Failed to save task {}, retry {}: {}", task_id, retry_count, e);
                std::thread::sleep(self.config.retry_delay);
                self.save_task_with_retry(task, retry_count + 1)
            }
        }
    }
    
    fn save_task_internal(&self, task: &Task, task_key: &str, version_key: &str, index_key: &str) -> Result<()> {
        // 转换为 protobuf 类型并序列化
        let proto_task = convert_task_to_proto(task);
        let mut serialized_task = Vec::new();
        proto_task.encode(&mut serialized_task)
            .map_err(|e| StorageError::SerializationError(e.to_string()))?;
        
        let version_bytes = task.version.to_le_bytes();
        
        let mut batch = WriteBatch::default();
        
        // Save task data
        batch.put_cf(
            self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, 
            task_key, 
            &serialized_task
        );
        
        // Save version
        batch.put_cf(
            self.db.cf_handle(TASK_VERSIONS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_VERSIONS_CF))?, 
            version_key, 
            &version_bytes
        );
        
        // Save index for efficient querying
        batch.put_cf(
            self.db.cf_handle(TASK_INDEX_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_INDEX_CF))?, 
            index_key, 
            task_key.as_bytes()
        );
        
        // Remove old index entries for this task
        self.cleanup_old_index_entries(&mut batch, &uuid::Uuid::from_bytes(task.id))?;
        
        self.db.write(batch)?;
        Ok(())
    }
    
    pub fn load_task(&self, task_id: &uuid::Uuid) -> Result<Option<Task>> {
        let task_key = format!("task:{}", task_id);
        
        match self.db.get_cf(self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, &task_key)? {
            Some(data) => {
                let proto_task = proto::Task::decode(&data[..])
                    .map_err(|e| StorageError::SerializationError(e.to_string()))?;
                let task = convert_task_from_proto(&proto_task).unwrap();
                Ok(Some(task))
            }
            None => Ok(None),
        }
    }
    
    pub fn load_all_tasks(&self) -> Result<Vec<Task>> {
        let iter = self.db.iterator_cf(
            self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, 
            IteratorMode::Start
        );
        
        let mut tasks = Vec::new();
        for item in iter {
            let (_, value) = item?;
            match proto::Task::decode(&value[..]) {
                Ok(proto_task) => {
                    match convert_task_from_proto(&proto_task) {
                        Ok(task) => tasks.push(task),
                        Err(e) => {
                            warn!("Failed to convert task from proto: {}", e);
                            continue;
                        }
                    }
                }
                Err(e) => {
                    warn!("Failed to deserialize task: {}", e);
                    continue;
                }
            }
        }
        
        // Sort by updated_at descending
        tasks.sort_by(|a, b| b.updated_at.cmp(&a.updated_at));
        
        Ok(tasks)
    }
    
    pub fn delete_task(&self, task_id: &uuid::Uuid) -> Result<()> {
        let task_key = format!("task:{}", task_id);
        let version_key = format!("version:{}", task_id);
        
        let mut batch = WriteBatch::default();
        
        // Delete task data
        batch.delete_cf(
            self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, 
            &task_key
        );
        
        // Delete version
        batch.delete_cf(
            self.db.cf_handle(TASK_VERSIONS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_VERSIONS_CF))?, 
            &version_key
        );
        
        // Clean up index entries
        self.cleanup_old_index_entries(&mut batch, task_id)?;
        
        self.db.write(batch)?;
        Ok(())
    }
    
    pub fn save_raft_state(&self, current_term: u64, voted_for: Option<PeerId>) -> Result<()> {
        let raft_state = proto::RaftState {
            current_term,
            voted_for: voted_for.map(|p| p.to_bytes()).unwrap_or_default(),
        };
        
        let mut serialized = Vec::new();
        raft_state.encode(&mut serialized)
            .map_err(|e| StorageError::SerializationError(e.to_string()))?;
        
        self.db.put_cf(
            self.db.cf_handle(RAFT_STATE_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", RAFT_STATE_CF))?, 
            "raft_state", 
            serialized
        )?;
        Ok(())
    }
    
    pub fn load_raft_state(&self) -> Result<(u64, Option<PeerId>)> {
        match self.db.get_cf(
            self.db.cf_handle(RAFT_STATE_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", RAFT_STATE_CF))?, 
            "raft_state"
        )? {
            Some(data) => {
                let raft_state = proto::RaftState::decode(&data[..])
                    .map_err(|e| StorageError::SerializationError(e.to_string()))?;
                
                let voted_for = if raft_state.voted_for.is_empty() {
                    None
                } else {
                    PeerId::from_bytes(&raft_state.voted_for).ok()
                };
                
                Ok((raft_state.current_term, voted_for))
            }
            None => Ok((0, None)),
        }
    }
    
    pub fn get_tasks_by_status(&self, status: &TaskStatus) -> Result<Vec<Task>> {
        let status_prefix = format!("index:{}:", status.status_string());
        
        let mut tasks = Vec::new();
        let iter = self.db.prefix_iterator_cf(
            self.db.cf_handle(TASK_INDEX_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_INDEX_CF))?, 
            &status_prefix
        );
        
        for item in iter {
            let (_, task_key) = item?;
            let task_key_str = String::from_utf8_lossy(&task_key);
            
            if let Ok(Some(task_data)) = self.db.get_cf(
                self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, 
                &*task_key_str
            ) {
                if let Ok(proto_task) = proto::Task::decode(&task_data[..]) {
                    if let Ok(task) = convert_task_from_proto(&proto_task) {
                        tasks.push(task);
                    }
                }
            }
        }
        
        Ok(tasks)
    }
    
    pub fn compact(&self) -> Result<()> {
        self.db.compact_range::<&[u8], &[u8]>(None, None);
        Ok(())
    }
    
    pub fn get_statistics(&self) -> Option<String> {
        if self.config.enable_statistics {
            self.db.property_value("rocksdb.stats").ok().flatten()
        } else {
            None
        }
    }
    
    pub fn backup(&self, backup_path: &PathBuf) -> Result<()> {
        let mut backup_engine = rocksdb::backup::BackupEngine::open(
            &rocksdb::backup::BackupEngineOptions::new(backup_path)?,
            &rocksdb::Env::new()?
        )?;
        
        backup_engine.create_new_backup(&self.db)?;
        Ok(())
    }
    
    pub fn restore_from_backup(&self, backup_path: &PathBuf, restore_path: &PathBuf) -> Result<()> {
        let mut backup_engine = rocksdb::backup::BackupEngine::open(
            &rocksdb::backup::BackupEngineOptions::new(backup_path)?,
            &rocksdb::Env::new()?
        )?;
        
        backup_engine.restore_from_latest_backup(restore_path, restore_path, &Default::default())?;
        Ok(())
    }
    
    fn get_cf(&self, name: &str) -> Result<&ColumnFamily> {
        self.db.cf_handle(name)
            .ok_or_else(|| anyhow::anyhow!("Column family {} not found", name))
    }
    
    fn cleanup_old_index_entries(&self, batch: &mut WriteBatch, task_id: &uuid::Uuid) -> Result<()> {
        let _cf = self.get_cf(TASK_INDEX_CF)?;
        let task_id_str = task_id.to_string();
        
        // Find and delete old index entries for this task
        let iter = self.db.iterator_cf(
            self.db.cf_handle(TASK_INDEX_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_INDEX_CF))?, 
            IteratorMode::Start
        );
        for item in iter {
            let (key, value) = item?;
            // let key_str = String::from_utf8_lossy(&key);
            let value_str = String::from_utf8_lossy(&value);
            
            if value_str.contains(&task_id_str) {
                batch.delete_cf(
                    self.db.cf_handle(TASK_INDEX_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_INDEX_CF))?, 
                    &key
                );
            }
        }
        
        Ok(())
    }

    pub fn cleanup_old_data(&self, config: &CleanupConfig) -> Result<CleanupStats> {
        let mut stats = CleanupStats::default();
        
        // 清理过期任务
        stats.expired_tasks = self.cleanup_expired_tasks(config.max_task_age_days)?;
        
        // 清理过多的已完成任务
        stats.excess_completed = self.cleanup_excess_tasks(
            &TaskStatus::Completed, 
            config.max_completed_tasks
        )?;
        
        // 清理过多的失败任务
        stats.excess_failed = self.cleanup_excess_failed_tasks(config.max_failed_tasks)?;
        
        // 清理孤立的索引条目
        stats.orphaned_indexes = self.cleanup_orphaned_indexes()?;
        
        // 压缩数据库
        if config.compact_after_cleanup {
            self.compact()?;
            stats.compacted = true;
        }
        
        info!("数据清理完成: {:?}", stats);
        Ok(stats)
    }
    
    fn cleanup_expired_tasks(&self, max_age_days: u32) -> Result<usize> {
        let cutoff_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs() - (max_age_days as u64 * 24 * 3600);
        
        let mut deleted_count = 0;
        let mut batch = WriteBatch::default();
        
        let iter = self.db.iterator_cf(
            self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, 
            IteratorMode::Start
        );
        
        for item in iter {
            let (key, value) = item?;
            
            if let Ok(proto_task) = proto::Task::decode(&value[..]) {
                if proto_task.updated_at < cutoff_time.try_into()? {
                    if let Ok(task) = convert_task_from_proto(&proto_task) {
                        let task_id = uuid::Uuid::from_bytes(task.id);
                        
                        // 删除任务数据
                        batch.delete_cf(
                            self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, 
                            &key
                        );
                        
                        // 删除版本信息
                        let version_key = format!("version:{}", task_id);
                        batch.delete_cf(
                            self.db.cf_handle(TASK_VERSIONS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_VERSIONS_CF))?, 
                            &version_key
                        );
                        
                        // 清理索引
                        self.cleanup_old_index_entries(&mut batch, &task_id)?;
                        
                        deleted_count += 1;
                        
                        // 批量提交以避免内存过多使用
                        if deleted_count % 1000 == 0 {
                            self.db.write(batch)?;
                            batch = WriteBatch::default();
                        }
                    }
                }
            }
        }
        
        if deleted_count % 1000 != 0 {
            self.db.write(batch)?;
        }
        
        Ok(deleted_count)
    }
    
    fn cleanup_excess_tasks(&self, status: &TaskStatus, max_count: usize) -> Result<usize> {
        let mut tasks = self.get_tasks_by_status(status)?;
        
        if tasks.len() <= max_count {
            return Ok(0);
        }
        
        // 按更新时间排序，保留最新的
        tasks.sort_by(|a, b| b.updated_at.cmp(&a.updated_at));
        
        let to_delete = &tasks[max_count..];
        let mut deleted_count = 0;
        let mut batch = WriteBatch::default();
        
        for task in to_delete {
            let task_id = uuid::Uuid::from_bytes(task.id);
            let task_key = format!("task:{}", task_id);
            let version_key = format!("version:{}", task_id);
            
            batch.delete_cf(
                self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, 
                &task_key
            );
            batch.delete_cf(
                self.db.cf_handle(TASK_VERSIONS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_VERSIONS_CF))?, 
                &version_key
            );
            self.cleanup_old_index_entries(&mut batch, &task_id)?;
            
            deleted_count += 1;
            
            if deleted_count % 1000 == 0 {
                self.db.write(batch)?;
                batch = WriteBatch::default();
            }
        }
        
        if deleted_count % 1000 != 0 {
            self.db.write(batch)?;
        }
        
        Ok(deleted_count)
    }
    
    fn cleanup_excess_failed_tasks(&self, max_count: usize) -> Result<usize> {
        // 获取所有失败任务的前缀
        let failed_prefix = "index:failed:";
        let mut failed_tasks = Vec::new();
        
        let iter = self.db.prefix_iterator_cf(
            self.db.cf_handle(TASK_INDEX_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_INDEX_CF))?, 
            failed_prefix
        );
        
        for item in iter {
            let (_, task_key) = item?;
            let task_key_str = String::from_utf8_lossy(&task_key);
            
            if let Ok(Some(task_data)) = self.db.get_cf(
                self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, 
                &*task_key_str
            ) {
                if let Ok(proto_task) = proto::Task::decode(&task_data[..]) {
                    if let Ok(task) = convert_task_from_proto(&proto_task) {
                        failed_tasks.push(task);
                    }
                }
            }
        }
        
        if failed_tasks.len() <= max_count {
            return Ok(0);
        }
        
        // 按更新时间排序，删除最旧的
        failed_tasks.sort_by(|a, b| a.updated_at.cmp(&b.updated_at));
        
        let to_delete = &failed_tasks[..failed_tasks.len() - max_count];
        let mut deleted_count = 0;
        let mut batch = WriteBatch::default();
        
        for task in to_delete {
            let task_id = uuid::Uuid::from_bytes(task.id);
            let task_key = format!("task:{}", task_id);
            let version_key = format!("version:{}", task_id);
            
            batch.delete_cf(
                self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, 
                &task_key
            );
            batch.delete_cf(
                self.db.cf_handle(TASK_VERSIONS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_VERSIONS_CF))?, 
                &version_key
            );
            self.cleanup_old_index_entries(&mut batch, &task_id)?;
            
            deleted_count += 1;
        }
        
        self.db.write(batch)?;
        Ok(deleted_count)
    }
    
    fn cleanup_orphaned_indexes(&self) -> Result<usize> {
        let mut orphaned_count = 0;
        let mut batch = WriteBatch::default();
        
        let iter = self.db.iterator_cf(
            self.db.cf_handle(TASK_INDEX_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_INDEX_CF))?, 
            IteratorMode::Start
        );
        
        for item in iter {
            let (key, value) = item?;
            let task_key_str = String::from_utf8_lossy(&value);
            
            // 检查对应的任务是否存在
            if self.db.get_cf(
                self.db.cf_handle(TASKS_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASKS_CF))?, 
                &*task_key_str
            )?.is_none() {
                batch.delete_cf(
                    self.db.cf_handle(TASK_INDEX_CF).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", TASK_INDEX_CF))?, 
                    &key
                );
                orphaned_count += 1;
                
                if orphaned_count % 1000 == 0 {
                    self.db.write(batch)?;
                    batch = WriteBatch::default();
                }
            }
        }
        
        if orphaned_count % 1000 != 0 {
            self.db.write(batch)?;
        }
        
        Ok(orphaned_count)
    }
    
    pub fn get_database_stats(&self) -> Result<DatabaseStats> {
        let mut stats = DatabaseStats::default();
        
        // 统计各种任务数量
        stats.total_tasks = self.count_tasks_in_cf(TASKS_CF)?;
        stats.pending_tasks = self.get_tasks_by_status(&TaskStatus::Pending)?.len();
        stats.completed_tasks = self.get_tasks_by_status(&TaskStatus::Completed)?.len();
        
        // 获取数据库大小信息
        if let Some(size_str) = self.db.property_value("rocksdb.total-sst-files-size")? {
            stats.database_size_bytes = size_str.parse().unwrap_or(0);
        }
        
        Ok(stats)
    }
    
    fn count_tasks_in_cf(&self, cf_name: &str) -> Result<usize> {
        let iter = self.db.iterator_cf(
            self.db.cf_handle(cf_name).ok_or_else(|| anyhow::anyhow!("Column family not found: {}", cf_name))?, 
            IteratorMode::Start
        );
        Ok(iter.count())
    }
}

// Implement Drop for graceful shutdown
impl Drop for DataStore {
    fn drop(&mut self) {
        info!("Shutting down RocksDB");
        // RocksDB handles cleanup automatically when Arc is dropped
    }
}

#[derive(Debug, Default)]
pub struct CleanupStats {
    pub expired_tasks: usize,
    pub excess_completed: usize,
    pub excess_failed: usize,
    pub orphaned_indexes: usize,
    pub compacted: bool,
}

#[derive(Debug, Default)]
pub struct DatabaseStats {
    pub total_tasks: usize,
    pub pending_tasks: usize,
    pub completed_tasks: usize,
    pub database_size_bytes: u64,
}