{"rustc": 14799578172954012516, "features": "[]", "declared_features": "[]", "target": 4182044030533789848, "profile": 7048363180732113261, "path": 7441990035522075502, "deps": [[1592167293562654925, "quick_protobuf", false, 3970214891854626158], [2706460456408817945, "futures", false, 14666966861404315886], [2918476194335491944, "libp2p_identity", false, 7591327888700463884], [3666196340704888985, "smallvec", false, 9056791839881781258], [7653847609259889164, "futures_bounded", false, 17424905838727556592], [8071766443291471244, "asynchronous_codec", false, 15946610308143125352], [8140693133181067772, "futures_timer", false, 1024389511199120988], [8606274917505247608, "tracing", false, 11094703943940771815], [10806645703491011684, "thiserror", false, 12398725825002675698], [11056201256703972851, "libp2p_core", false, 3299821058173122628], [12170264697963848012, "either", false, 18031249821773630939], [13637209728358017040, "libp2p_swarm", false, 15197219675559249253], [16265541232480197760, "quick_protobuf_codec", false, 11752230470593640486]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libp2p-identify-270dcc04f57b4d61\\dep-lib-libp2p_identify", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}