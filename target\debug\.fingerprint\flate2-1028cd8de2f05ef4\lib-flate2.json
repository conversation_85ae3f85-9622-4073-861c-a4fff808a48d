{"rustc": 14799578172954012516, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15657897354478470176, "path": 16347078795833691774, "deps": [[7312356825837975969, "crc32fast", false, 6089441174294016208], [7636735136738807108, "miniz_oxide", false, 3540013792748135494]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-1028cd8de2f05ef4\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}