use std::{
    collections::{HashMap, HashSet, VecDeque},
    error::Error,
    time::Duration,
    path::PathBuf,
};

use futures::stream::StreamExt;
use libp2p::{
    gossipsub, identify, identity::Keypair, mdns, 
    swarm::SwarmEvent, PeerId, Swarm,
};
use tokio::{
    sync::{mpsc, oneshot, RwLock}, 
    time::interval
};
use std::sync::Arc;
use log::{error, info, warn, debug};

use crate::{
    messages::QueueMessage, network::{GossipsubEvent, MdnsEvent, TaskQueueBehaviour}, raft::*, storage::DataStore, types::*, TaskQueueBehaviourEvent, storage::CleanupConfig
};

pub struct DistributedTaskQueue {
    swarm: Swarm<TaskQueueBehaviour>,
    tasks: HashMap<uuid::Uuid, Task>,
    processing_tasks: HashSet<uuid::Uuid>,
    available_workers: HashSet<PeerId>,
    cmd_rx: mpsc::UnboundedReceiver<Command>,
    cmd_tx: mpsc::UnboundedSender<Command>,
    peer_id: PeerId,
    topic: gossipsub::IdentTopic,
    message_cache: VecDeque<CachedMessage>,
    connection_tracker: Arc<RwLock<ConnectionTracker>>,
    data_store: DataStore,
    sync_requests: HashMap<uuid::Uuid, std::time::Instant>,
    processed_messages: HashSet<String>,
    task_versions: HashMap<uuid::Uuid, u64>,
    raft_state: RaftState,
    device_tracker: Arc<RwLock<DeviceTracker>>,
}


impl DistributedTaskQueue {
    pub async fn new(data_dir: Option<PathBuf>) -> Result<(Self, mpsc::UnboundedSender<Command>), Box<dyn Error>> {
        let keypair = Keypair::generate_ed25519();
        let peer_id = PeerId::from(keypair.public());
        
        info!("节点 PeerId: {}", peer_id);

        let message_id_fn = |message: &gossipsub::Message| {
            let mut hasher = std::collections::hash_map::DefaultHasher::new();
            std::hash::Hash::hash(&message.data, &mut hasher);
            std::hash::Hash::hash(&message.sequence_number, &mut hasher);
            std::hash::Hash::hash(&message.source, &mut hasher);
            gossipsub::MessageId::from(std::hash::Hasher::finish(&hasher).to_string())
        };

        let gossipsub_config = gossipsub::ConfigBuilder::default()
            .heartbeat_interval(Duration::from_secs(1))
            .validation_mode(gossipsub::ValidationMode::Strict)
            .duplicate_cache_time(Duration::from_secs(300))
            .message_id_fn(message_id_fn)
            .flood_publish(true)
            .build()
            .expect("Valid config");

        let mut gossipsub = gossipsub::Behaviour::new(
            gossipsub::MessageAuthenticity::Signed(keypair.clone()),
            gossipsub_config,
        )?;

        let topic = gossipsub::IdentTopic::new("farm-task-queue");
        gossipsub.subscribe(&topic)?;

        let mdns = mdns::tokio::Behaviour::new(mdns::Config::default(), peer_id)?;
        let identify = identify::Behaviour::new(identify::Config::new(
            "/farm-task-queue/1.0.0".to_string(),
            keypair.public(),
        ));

        let mut swarm = libp2p::SwarmBuilder::with_existing_identity(keypair)
            .with_tokio()
            .with_quic()
            .with_behaviour(|_| TaskQueueBehaviour { gossipsub, mdns, identify })?
            .build();
        
        swarm.listen_on("/ip4/0.0.0.0/udp/0/quic-v1".parse()?)?;

        let (cmd_tx, cmd_rx) = mpsc::unbounded_channel();

        let data_path = data_dir.unwrap_or_else(|| PathBuf::from(format!("./data/{}", peer_id)));
        let config = crate::storage::RocksDbConfig {
            database_path: data_path,
            ..Default::default()
        };
        let data_store = DataStore::new(config)?;

        let loaded_tasks = data_store.load_all_tasks()?;
        let mut tasks = HashMap::new();
        let mut task_versions = HashMap::new();
        
        for task in loaded_tasks {
            task_versions.insert(uuid::Uuid::from_bytes(task.id), task.version);
            tasks.insert(uuid::Uuid::from_bytes(task.id), task);
        }
        
        let mut raft_state = RaftState::new();
        if let Ok((term, voted_for)) = data_store.load_raft_state() {
            raft_state.current_term = term;
            raft_state.voted_for = voted_for;
        }

        let device_tracker = Arc::new(RwLock::new(DeviceTracker::default()));

        Ok((
            Self {
                swarm,
                tasks,
                processing_tasks: HashSet::new(),
                available_workers: HashSet::new(),
                cmd_rx,
                cmd_tx: cmd_tx.clone(),
                peer_id,
                topic,
                message_cache: VecDeque::new(),
                connection_tracker: Arc::new(RwLock::new(ConnectionTracker::default())),
                data_store,
                sync_requests: HashMap::new(),
                processed_messages: HashSet::new(),
                task_versions,
                raft_state,
                device_tracker,
            },
            cmd_tx.clone()
        ))
    }

    pub async fn run(mut self) {
        let mut heartbeat = interval(Duration::from_millis(100));
        let mut election_check = interval(Duration::from_millis(20));
        let mut retry_timer = interval(Duration::from_secs(5));
        let mut sync_timer = interval(Duration::from_secs(30));
        let mut cleanup_timer = interval(Duration::from_secs(60));
        let mut db_cleanup_timer = interval(Duration::from_secs(3600)); // 1小时清理一次

        loop {
            tokio::select! {
                Some(cmd) = self.cmd_rx.recv() => self.handle_command(cmd).await,
                event = self.swarm.select_next_some() => self.handle_swarm_event(event).await,
                _ = heartbeat.tick() => {
                    if self.raft_state.state == NodeState::Leader {
                        self.send_heartbeat().await;
                        self.assign_pending_tasks().await;
                    }
                }
                _ = election_check.tick() => {
                    if self.raft_state.state != NodeState::Leader && self.raft_state.is_election_timeout() {
                        let tracker = self.connection_tracker.read().await;
                        let connected_count = tracker.connected_peers.len();
                        let subscribed_count = tracker.get_subscribed_count();
                        drop(tracker);
                        
                        if connected_count == 0 && subscribed_count == 0 {
                            info!("选举检查时发现单节点，直接成为Leader，保持任期: {}", 
                                  self.raft_state.current_term);
                            self.raft_state.become_leader(self.peer_id);
                            self.save_raft_state();
                        } else {
                            self.start_election().await;
                        }
                    }
                }
                _ = retry_timer.tick() => self.retry_cached_messages().await,
                _ = sync_timer.tick() => self.sync_tasks().await,
                _ = cleanup_timer.tick() => self.cleanup_old_messages(),
                _ = db_cleanup_timer.tick() => {
                    // 只有Leader执行数据库清理，避免多节点同时清理
                    if self.raft_state.state == NodeState::Leader {
                        self.cleanup_database().await;
                    }
                }
            }
        }
    }

    async fn handle_command(&mut self, cmd: Command) {
        match cmd {
            Command::SubmitTask { task_type, payload, response } => {
                let task = Task::new(task_type, payload.to_string());
                
                let task_id = uuid::Uuid::from_bytes(task.id);
                self.tasks.insert(task_id, task.clone());
                self.task_versions.insert(task_id, task.version);
                
                if let Err(e) = self.data_store.save_task(&task) {
                    error!("保存任务失败: {}", e);
                }
                
                let message = QueueMessage::NewTask(task.clone());
                match self.publish_message_with_retry(message).await {
                    Ok(_) => {
                        info!("发布新任务: {}", task_id);
                        let _ = response.send(Ok(task_id));
                    }
                    Err(e) => {
                        warn!("发布任务失败: {}", e);
                        let _ = response.send(Ok(task_id));
                    }
                }
            }
            
            Command::ProcessNextTask { response } => {
                // 查找优先级最高的待处理任务
                let next_task = self.tasks
                    .values()
                    .filter(|task| matches!(task.status, TaskStatus::Pending))
                    .max_by(|a, b| {
                        // 按优先级降序，然后按创建时间升序
                        b.priority.cmp(&a.priority).then(a.created_at.cmp(&b.created_at))
                    })
                    .cloned();
                
                let _ = response.send(next_task);
            }
            
            Command::GetTaskStatus { task_id, response } => {
                let task = self.tasks.get(&task_id).cloned();
                let _ = response.send(task);
            }
            
            Command::GetConnectedPeers { response } => {
                let tracker = self.connection_tracker.read().await;
                let peers: Vec<PeerId> = tracker.connected_peers.iter().cloned().collect();
                let _ = response.send(peers);
            }
            
            Command::GetQueueStats { response } => {
                let stats = self.calculate_stats();
                let _ = response.send(stats);
            }
            
            Command::GetLeaderInfo { response } => {
                let info = LeaderInfo {
                    is_leader: self.raft_state.state == NodeState::Leader,
                    leader_id: self.raft_state.leader_id,
                    current_term: self.raft_state.current_term,
                    state: format!("{:?}", self.raft_state.state),
                };
                let _ = response.send(info);
            }
            
            Command::CleanupDatabase => {
                // 手动触发数据库清理
                if self.raft_state.state == NodeState::Leader {
                    self.cleanup_database().await;
                } else {
                    warn!("只有Leader节点可以执行数据库清理");
                }
            }
            
            Command::GetDatabaseStats => {
                // 获取并打印数据库统计信息
                match self.data_store.get_database_stats() {
                    Ok(stats) => {
                        info!("数据库统计信息:");
                        info!("  总任务数: {}", stats.total_tasks);
                        info!("  待处理任务: {}", stats.pending_tasks);
                        info!("  已完成任务: {}", stats.completed_tasks);
                        info!("  数据库大小: {}MB", stats.database_size_bytes / 1024 / 1024);
                    }
                    Err(e) => {
                        error!("获取数据库统计信息失败: {}", e);
                    }
                }
            }
            
            Command::ElectionTimeout { term } => {
                if term == self.raft_state.current_term && self.raft_state.state != NodeState::Leader {
                    self.start_election().await;
                }
            }
            Command::GetAllTasks { response } => {
                match self.data_store.load_all_tasks() {
                    Ok(tasks) => {
                        let _ = response.send(Ok(tasks));
                    }
                    Err(e) => {
                        let _ = response.send(Err(format!("获取任务列表失败: {}", e)));
                    }
                }
            }
            Command::UpdateTask { task_id, task_type, payload, priority, response } => {
                if let Some(task) = self.tasks.get_mut(&task_id) {
                    // 只允许修改待处理的任务
                    if !matches!(task.status, TaskStatus::Pending) {
                        let _ = response.send(Err("只能修改待处理状态的任务".to_string()));
                        return;
                    }
                    
                    let mut updated = false;
                    
                    // 更新任务类型
                    if let Some(new_task_type) = task_type {
                        if task.task_type != new_task_type {
                            task.task_type = new_task_type;
                            updated = true;
                        }
                    }
                    
                    // 更新载荷
                    if let Some(new_payload) = payload {
                        if task.payload != new_payload {
                            task.payload = new_payload;
                            updated = true;
                        }
                    }
                    
                    // 更新优先级
                    if let Some(new_priority) = priority {
                        if task.priority != new_priority {
                            task.priority = new_priority;
                            updated = true;
                        }
                    }
                    
                    if updated {
                        task.updated_at = chrono::Utc::now().timestamp();
                        task.version += 1;
                        self.task_versions.insert(task_id, task.version);
                        
                        // 保存到数据库
                        if let Err(e) = self.data_store.save_task(&task) {
                            error!("保存更新任务失败: {}", e);
                            let _ = response.send(Err(format!("保存任务失败: {}", e)));
                            return;
                        }
                        
                        // 广播任务更新
                        let message = QueueMessage::UpdateTask { task: task.clone() };
                        match self.publish_message_with_retry(message).await {
                            Ok(_) => {
                                info!("任务已更新: {}", task_id);
                                let _ = response.send(Ok(()));
                            }
                            Err(e) => {
                                warn!("广播任务更新失败: {}", e);
                                let _ = response.send(Ok(())); // 本地更新成功
                            }
                        }
                    } else {
                        let _ = response.send(Ok(())); // 没有变化
                    }
                } else {
                    let _ = response.send(Err("任务不存在".to_string()));
                }
            }
            Command::DeleteTask { task_id, response } => {
                if let Some(task) = self.tasks.get(&task_id) {
                    // 只允许删除待处理的任务
                    if !matches!(task.status, TaskStatus::Pending) {
                        let _ = response.send(Err("只能删除待处理状态的任务".to_string()));
                        return;
                    }
                    
                    let task_version = task.version;
                    
                    // 从内存中删除任务
                    self.tasks.remove(&task_id);
                    self.task_versions.remove(&task_id);
                    
                    // 从数据库中删除任务
                    if let Err(e) = self.data_store.delete_task(&task_id) {
                        error!("删除任务失败: {}", e);
                        let _ = response.send(Err(format!("删除任务失败: {}", e)));
                        return;
                    }
                    
                    // 广播任务删除
                    let message = QueueMessage::DeleteTask { 
                        task_id: task_id.into_bytes(), 
                        version: task_version 
                    };
                    match self.publish_message_with_retry(message).await {
                        Ok(_) => {
                            info!("任务已删除: {}", task_id);
                            let _ = response.send(Ok(()));
                        }
                        Err(e) => {
                            warn!("广播任务删除失败: {}", e);
                            let _ = response.send(Ok(())); // 本地删除成功
                        }
                    }
                } else {
                    let _ = response.send(Err("任务不存在".to_string()));
                }
            }
            Command::CompleteTask { task_id } => {
                self.complete_task(task_id).await;
            }
            Command::FailTask { task_id, reason } => {
                self.fail_task(task_id, reason).await;
            }
            Command::GetAllDeviceInfo { response } => {
                let device_tracker = self.device_tracker.read().await;
                let device_info = device_tracker.device_info.clone();
                let _ = response.send(Ok(device_info));
            }
        }
    }

    async fn handle_swarm_event(&mut self, event: SwarmEvent<TaskQueueBehaviourEvent>) {
        match event {
            SwarmEvent::Behaviour(TaskQueueBehaviourEvent::Gossipsub(
                GossipsubEvent::Message { message_id: id, message, .. },
            )) => {
                if !self.processed_messages.contains(&id.to_string()) {
                    self.processed_messages.insert(id.to_string());
                    self.handle_gossip_message(message).await;
                }
            }
            
            SwarmEvent::Behaviour(TaskQueueBehaviourEvent::Gossipsub(
                GossipsubEvent::Subscribed { peer_id, .. }
            )) => {
                info!("节点 {} 订阅了主题", peer_id);
                let mut tracker = self.connection_tracker.write().await;
                tracker.mark_subscribed(peer_id);
                drop(tracker);
                
                self.request_sync(peer_id).await;
                
                // 如果是Leader且该节点没有设备信息，主动请求
                if self.raft_state.state == NodeState::Leader {
                    let device_tracker = self.device_tracker.read().await;
                    let has_device_info = device_tracker.has_device_info(&peer_id);
                    drop(device_tracker);
                    
                    if !has_device_info {
                        let message = QueueMessage::DeviceInfoRequest {
                            from: self.peer_id.to_bytes(),
                        };
                        let _ = self.publish_message(message).await;
                        info!("Leader请求订阅节点 {} 的设备信息", peer_id);
                    }
                }
            }
            
            SwarmEvent::Behaviour(TaskQueueBehaviourEvent::Mdns(event)) => {
                match event {
                    MdnsEvent::Discovered(list) => {
                        for (peer_id, _) in list {
                            info!("发现新节点: {}", peer_id);
                            self.swarm.behaviour_mut().gossipsub.add_explicit_peer(&peer_id);
                            
                            let mut tracker = self.connection_tracker.write().await;
                            tracker.add_peer(peer_id);
                        }
                    }
                    MdnsEvent::Expired(list) => {
                        for (peer_id, _) in list {
                            self.swarm.behaviour_mut().gossipsub.remove_explicit_peer(&peer_id);
                            
                            let mut tracker = self.connection_tracker.write().await;
                            tracker.remove_peer(&peer_id);
                            drop(tracker);
                            
                            // 从设备跟踪器中移除过期节点的设备信息
                            let mut device_tracker = self.device_tracker.write().await;
                            device_tracker.remove_device_info(&peer_id);
                            drop(device_tracker);
                            
                            info!("已清理过期节点 {} 的设备信息", peer_id);
                            
                            if Some(peer_id) == self.raft_state.leader_id {
                                warn!("Leader {} 已断开连接", peer_id);
                                self.raft_state.leader_id = None;
                                self.raft_state.reset_election_timer();
                                
                                // 立即检查是否为单节点
                                let tracker = self.connection_tracker.read().await;
                                let connected_count = tracker.connected_peers.len();
                                let subscribed_count = tracker.get_subscribed_count();
                                drop(tracker);
                                
                                if connected_count == 0 && subscribed_count == 0 {
                                    info!("mDNS过期后检测到单节点，立即成为Leader");
                                    self.raft_state.become_leader(self.peer_id);
                                    self.save_raft_state();
                                }
                            }
                        }
                    }
                }
            }
            
            SwarmEvent::ConnectionEstablished { peer_id, .. } => {
                info!("与节点 {} 建立连接", peer_id);
                let mut tracker = self.connection_tracker.write().await;
                tracker.add_peer(peer_id);
                drop(tracker);
                
                // 请求新连接节点的设备信息
                self.request_sync(peer_id).await;
                
                // 如果是Leader，主动请求该节点的设备信息
                if self.raft_state.state == NodeState::Leader {
                    let message = QueueMessage::DeviceInfoRequest {
                        from: self.peer_id.to_bytes(),
                    };
                    let _ = self.publish_message(message).await;
                    info!("Leader请求新连接节点 {} 的设备信息", peer_id);
                }
            }
            
            SwarmEvent::ConnectionClosed { peer_id, .. } => {
                info!("与节点 {} 断开连接", peer_id);
                
                // 从连接跟踪器中移除节点
                let mut tracker = self.connection_tracker.write().await;
                tracker.remove_peer(&peer_id);
                drop(tracker);
                
                // 从设备跟踪器中移除设备信息
                let mut device_tracker = self.device_tracker.write().await;
                device_tracker.remove_device_info(&peer_id);
                drop(device_tracker);
                
                info!("已清理节点 {} 的设备信息", peer_id);
                
                // 如果断开的是Leader，立即检查是否应该开始选举
                if Some(peer_id) == self.raft_state.leader_id {
                    warn!("Leader {} 已断开连接", peer_id);
                    self.raft_state.leader_id = None;
                    self.raft_state.reset_election_timer();
                    
                    // 立即检查是否为单节点，如果是则直接成为Leader
                    let tracker = self.connection_tracker.read().await;
                    let connected_count = tracker.connected_peers.len();
                    let subscribed_count = tracker.get_subscribed_count();
                    drop(tracker);
                    
                    if connected_count == 0 && subscribed_count == 0 {
                        info!("Leader断开后检测到单节点，立即成为新Leader");
                        self.raft_state.become_leader(self.peer_id);
                        self.save_raft_state();
                    }
                }
            }
            
            SwarmEvent::NewListenAddr { address, .. } => {
                info!("监听地址: {}", address);
            }
            
            _ => {}
        }
    }

    async fn handle_gossip_message(&mut self, message: gossipsub::Message) {
        if let Ok(queue_msg) = QueueMessage::deserialize(&message.data) {
            // 在处理任何Raft消息前，检查是否应该立即成为Leader
            if matches!(queue_msg, 
                       QueueMessage::VoteRequest(_) | 
                       QueueMessage::VoteResponse(_) | 
                       QueueMessage::Heartbeat { .. }) {
                
                let tracker = self.connection_tracker.read().await;
                let connected_count = tracker.connected_peers.len();
                let subscribed_count = tracker.get_subscribed_count();
                drop(tracker);
                
                if connected_count == 0 && subscribed_count == 0 && 
                   self.raft_state.state != NodeState::Leader {
                    info!("处理消息时检测到单节点，立即成为Leader");
                    self.raft_state.become_leader(self.peer_id);
                    self.save_raft_state();
                    return;
                }
            }
            
            match queue_msg {
                QueueMessage::NewTask(task) => {
                    let task_id = uuid::Uuid::from_bytes(task.id);
                    if !self.tasks.contains_key(&task_id) {
                        info!("收到新任务: {} [{}]", task.task_type, task_id);
                        self.tasks.insert(task_id, task.clone());
                        self.task_versions.insert(task_id, task.version);
                        let _ = self.data_store.save_task(&task);
                    }
                }
                
                QueueMessage::AssignTask { task_id, worker, version } => {
                    let task_id = uuid::Uuid::from_bytes(task_id);
                    if let Some(task) = self.tasks.get_mut(&task_id) {
                        if task.version == version && matches!(task.status, TaskStatus::Pending) {
                            let is_my_task = worker == self.peer_id.to_bytes();
                            task.status = TaskStatus::Processing { worker: worker.clone() };
                            task.updated_at = chrono::Utc::now().timestamp();
                            task.version += 1;
                            self.task_versions.insert(task_id, task.version);
                            
                            if is_my_task {
                                info!("收到任务分配: {}", task_id);
                                self.processing_tasks.insert(task_id);
                            }
                            
                            let _ = self.data_store.save_task(&task);
                            
                            // 广播任务状态更新
                            let broadcast = QueueMessage::TaskStatusBroadcast { task: task.clone() };
                            let _ = self.publish_message(broadcast).await;
                        }
                    }
                }
                
                QueueMessage::RequestTask { worker } => {
                    if self.raft_state.state == NodeState::Leader {
                        if let Ok(peer_id) = PeerId::from_bytes(&worker) {
                            self.available_workers.insert(peer_id);
                        }
                    }
                }
                
                QueueMessage::CompleteTask { task_id, worker, version } => {
                    let task_id = uuid::Uuid::from_bytes(task_id);
                    if let Some(task) = self.tasks.get_mut(&task_id) {
                        if task.version == version {
                            task.status = TaskStatus::Completed;
                            task.updated_at = chrono::Utc::now().timestamp();
                            task.version += 1;
                            self.task_versions.insert(task_id, task.version);
                            
                            if worker == self.peer_id.to_bytes() {
                                info!("✓ 任务已完成: {}", task_id);
                                self.processing_tasks.remove(&task_id);
                            }
                            
                            let _ = self.data_store.save_task(&task);
                            
                            // 广播任务状态更新
                            let broadcast = QueueMessage::TaskStatusBroadcast { task: task.clone() };
                            let _ = self.publish_message(broadcast).await;
                        } else {
                            warn!("任务完成版本冲突: {} 期望版本 {} 实际版本 {}", 
                                  task_id, version, task.version);
                        }
                    }
                }
                
                QueueMessage::FailTask { task_id, worker, reason, version } => {
                    let task_id = uuid::Uuid::from_bytes(task_id);
                    if let Some(task) = self.tasks.get_mut(&task_id) {
                        if task.version == version {
                            task.retry_count += 1;
                            
                            if task.retry_count >= task.max_retries {
                                task.status = TaskStatus::Failed { reason: reason.clone() };
                                if worker == self.peer_id.to_bytes() {
                                    error!("✗ 任务失败: {} - {}", task_id, reason);
                                }
                            } else {
                                task.status = TaskStatus::Pending;
                                if worker == self.peer_id.to_bytes() {
                                    warn!("任务 {} 失败，准备重试 ({}/{})", 
                                        task_id, task.retry_count, task.max_retries);
                                }
                            }
                            
                            task.updated_at = chrono::Utc::now().timestamp();
                            task.version += 1;
                            self.task_versions.insert(task_id, task.version);
                            
                            let _ = self.data_store.save_task(&task);
                            
                            if worker == self.peer_id.to_bytes() {
                                self.processing_tasks.remove(&task_id);
                            }
                            
                            // 广播任务状态更新
                            let broadcast = QueueMessage::TaskStatusBroadcast { task: task.clone() };
                            let _ = self.publish_message(broadcast).await;
                        } else {
                            warn!("任务失败版本冲突: {} 期望版本 {} 实际版本 {}", 
                                  task_id, version, task.version);
                        }
                    }
                }
                
                QueueMessage::UpdateTask { task } => {
                    let task_id = uuid::Uuid::from_bytes(task.id);
                    let should_update = self.tasks.get(&task_id)
                        .map(|local_task| {
                            if local_task.version < task.version {
                                true
                            } else if local_task.version == task.version {
                                local_task.updated_at < task.updated_at
                            } else {
                                false
                            }
                        })
                        .unwrap_or(true);
                    
                    if should_update && matches!(task.status, TaskStatus::Pending) {
                        info!("同步任务更新: {} 版本 {} -> {}", task_id, 
                              self.tasks.get(&task_id).map(|t| t.version).unwrap_or(0), 
                              task.version);
                        self.tasks.insert(task_id, task.clone());
                        self.task_versions.insert(task_id, task.version);
                        let _ = self.data_store.save_task(&task);
                    }
                }
                
                QueueMessage::DeleteTask { task_id, version } => {
                    let task_uuid = uuid::Uuid::from_bytes(task_id);
                    
                    if let Some(local_task) = self.tasks.get(&task_uuid) {
                        if local_task.version <= version && matches!(local_task.status, TaskStatus::Pending) {
                            info!("同步删除任务: {} 版本: {}", task_uuid, version);
                            
                            self.tasks.remove(&task_uuid);
                            self.task_versions.remove(&task_uuid);
                            
                            if let Err(e) = self.data_store.delete_task(&task_uuid) {
                                error!("同步删除任务时数据库操作失败: {}", e);
                            }
                        }
                    }
                }
                
                // Raft messages
                QueueMessage::VoteRequest(vote_req) => self.handle_vote_request(vote_req).await,
                QueueMessage::VoteResponse(vote_resp) => self.handle_vote_response(vote_resp).await,
                QueueMessage::AppendEntries(append_entries) => self.handle_append_entries(append_entries).await,
                QueueMessage::AppendEntriesResponse { term, .. } => {
                    if term > self.raft_state.current_term {
                        self.raft_state.become_follower(term, None);
                        self.save_raft_state();
                    }
                }
                QueueMessage::Heartbeat { leader_id, term } => {
                    let leader_id = PeerId::from_bytes(&leader_id).unwrap();
                    if term >= self.raft_state.current_term {
                        if term > self.raft_state.current_term || self.raft_state.leader_id != Some(leader_id) {
                            self.raft_state.become_follower(term, Some(leader_id));
                            self.save_raft_state();
                        }
                        self.raft_state.reset_election_timer();
                    }
                }
                
                // Sync messages - simplified handling
                QueueMessage::TaskStatusBroadcast { task } => {
                    let task_id = uuid::Uuid::from_bytes(task.id);
                    let should_update = self.tasks.get(&task_id)
                        .map(|local_task| {
                            if local_task.version < task.version {
                                true
                            } else if local_task.version == task.version {
                                // 版本相同，检查时间戳
                                local_task.updated_at < task.updated_at
                            } else {
                                false
                            }
                        })
                        .unwrap_or(true);
                        
                    if should_update {
                        info!("同步任务状态: {} 版本 {} -> {}", task_id, 
                              self.tasks.get(&task_id).map(|t| t.version).unwrap_or(0), 
                              task.version);
                        self.tasks.insert(task_id, task.clone());
                        self.task_versions.insert(task_id, task.version);
                        let _ = self.data_store.save_task(&task);
                    }
                }
                
                QueueMessage::DeviceInfoRequest { from } => {
                    if let Ok(from_peer) = PeerId::from_bytes(&from) {
                        if from_peer != self.peer_id {
                            match DeviceInfo::collect() {
                                Ok(device_info) => {
                                    let response = QueueMessage::DeviceInfoResponse {
                                        peer_id: self.peer_id.to_bytes(),
                                        device_info,
                                    };
                                    let _ = self.publish_message(response).await;
                                    info!("发送设备信息给Leader {}", from_peer);
                                }
                                Err(e) => {
                                    warn!("收集设备信息失败: {}", e);
                                }
                            }
                        }
                    }
                }

                QueueMessage::DeviceInfoResponse { peer_id, device_info } => {
                    if let Ok(peer_id) = PeerId::from_bytes(&peer_id) {
                        if peer_id != self.peer_id {
                            let mut tracker = self.device_tracker.write().await;
                            tracker.update_device_info(peer_id, device_info.clone());
                            info!("收到节点 {} 的设备信息: CPU核心数={}, 可用内存={}GB", 
                                  peer_id, device_info.cpu_cores, 
                                  device_info.available_memory / (1024 * 1024 * 1024));
                        }
                    }
                }
                
                _ => {} // Handle other messages...
            }
        }
    }

    // Raft implementation (simplified)
    async fn start_election(&mut self) {
        // 强制检查当前连接状态
        let tracker = self.connection_tracker.read().await;
        let connected_count = tracker.connected_peers.len();
        let subscribed_count = tracker.get_subscribed_count();
        drop(tracker);
        
        info!("开始选举检查 - 连接节点: {}, 订阅节点: {}", connected_count, subscribed_count);
        
        // 如果没有其他节点，立即成为Leader，不增加任期
        if connected_count == 0 && subscribed_count == 0 {
            info!("检测到单节点场景，直接成为Leader，保持任期: {}", self.raft_state.current_term);
            self.raft_state.become_leader_without_term_increment(self.peer_id);
            self.save_raft_state();
            return;
        }
        
        // 成为候选人，但不增加任期
        self.raft_state.become_candidate(self.peer_id);
        self.save_raft_state();
        
        let vote_request = VoteRequest {
            term: self.raft_state.current_term,
            candidate_id: self.peer_id.into(),
            last_log_index: self.raft_state.log.len() as u64,
            last_log_term: self.raft_state.log.last().map(|e| e.term).unwrap_or(0),
        };
        
        // 设置选举超时
        let election_timeout = Duration::from_millis(1000);
        let cmd_tx = self.cmd_tx.clone();
        let current_term = self.raft_state.current_term;
        
        tokio::spawn(async move {
            tokio::time::sleep(election_timeout).await;
            let _ = cmd_tx.send(Command::ElectionTimeout { term: current_term });
        });
        
        info!("发送投票请求，任期: {}", self.raft_state.current_term);
        let _ = self.publish_message(QueueMessage::VoteRequest(vote_request)).await;
    }
    
    async fn handle_vote_request(&mut self, vote_req: VoteRequest) {
        let mut vote_granted = false;
        
        if vote_req.term > self.raft_state.current_term {
            self.raft_state.become_follower(vote_req.term, None);
            self.save_raft_state();
        }
        
        if vote_req.term == self.raft_state.current_term &&
           (self.raft_state.voted_for.is_none() || 
            self.raft_state.voted_for == Some(PeerId::from_bytes(&vote_req.candidate_id).unwrap())) {
            vote_granted = true;
            self.raft_state.voted_for = Some(PeerId::from_bytes(&vote_req.candidate_id).unwrap());
            self.raft_state.reset_election_timer();
            self.save_raft_state();
        }
        
        let response = VoteResponse {
            term: self.raft_state.current_term,
            vote_granted,
            voter_id: self.peer_id.into(),
        };
        
        let _ = self.publish_message(QueueMessage::VoteResponse(response)).await;
    }
    
    async fn handle_vote_response(&mut self, vote_resp: VoteResponse) {
        if self.raft_state.state != NodeState::Candidate {
            return;
        }
        
        if vote_resp.term > self.raft_state.current_term {
            self.raft_state.become_follower(vote_resp.term, None);
            self.save_raft_state();
            return;
        }
        
        if vote_resp.term == self.raft_state.current_term && vote_resp.vote_granted {
            if let Ok(voter_peer_id) = PeerId::from_bytes(&vote_resp.voter_id) {
                self.raft_state.votes_received.insert(voter_peer_id);
            }
            
            let tracker = self.connection_tracker.read().await;
            let connected_count = tracker.connected_peers.len();
            let subscribed_count = tracker.get_subscribed_count();
            let total_nodes = (connected_count.max(subscribed_count)) + 1;
            drop(tracker);
            
            let votes_needed = (total_nodes / 2) + 1;
            
            if self.raft_state.votes_received.len() >= votes_needed {
                // 选举成功，成为Leader并增加任期
                self.raft_state.become_leader(self.peer_id);
                self.save_raft_state();
                self.send_heartbeat().await;
            }
        }
    }
    
    async fn handle_append_entries(&mut self, append_entries: AppendEntries) {
        if append_entries.term >= self.raft_state.current_term {
            if append_entries.term > self.raft_state.current_term {
                self.raft_state.become_follower(append_entries.term, Some(PeerId::from_bytes(&append_entries.leader_id).unwrap()));
                self.save_raft_state();
            }
            self.raft_state.reset_election_timer();
            self.raft_state.leader_id = Some(PeerId::from_bytes(&append_entries.leader_id).unwrap());
        }
        
        let response = QueueMessage::AppendEntriesResponse {
            term: self.raft_state.current_term,
            success: true,
            follower_id: self.peer_id.into(),
        };
        
        let _ = self.publish_message(response).await;
    }
    
    async fn send_heartbeat(&mut self) {
        if self.raft_state.state != NodeState::Leader {
            return;
        }
        
        let message = QueueMessage::Heartbeat {
            leader_id: self.peer_id.into(),
            term: self.raft_state.current_term,
        };
        
        let _ = self.publish_message(message).await;
    }
    
    async fn assign_pending_tasks(&mut self) {
        if self.raft_state.state != NodeState::Leader {
            return;
        }

        // Leader发布任务前先请求所有节点的设备信息
        self.request_all_device_info().await;
        
        // 等待设备信息收集完成
        tokio::time::sleep(Duration::from_millis(500)).await;

        let mut pending_tasks: Vec<_> = self.tasks
            .iter()
            .filter(|(_, task)| matches!(task.status, TaskStatus::Pending))
            .map(|(id, task)| (*id, task.version, task.priority, task.created_at))
            .collect();

        pending_tasks.sort_by(|a, b| {
            b.2.cmp(&a.2).then(a.3.cmp(&b.3))
        });

        let workers: Vec<_> = self.available_workers.iter().cloned().collect();

        for ((task_id, version, _, _), worker) in pending_tasks.iter().zip(workers.iter()) {
            // 先检查任务是否存在且状态正确，避免在循环中持有可变借用
            let task_valid = self.tasks.get(&task_id)
                .map(|task| task.version == *version && matches!(task.status, TaskStatus::Pending))
                .unwrap_or(false);
                
            if !task_valid {
                continue;
            }

            // 获取任务类型用于确定需求
            let task_type = self.tasks.get(&task_id).unwrap().task_type.clone();
            let requirement = self.determine_task_requirement_by_type(&task_type);
            
            // 获取最佳worker
            let best_workers = {
                let device_tracker = self.device_tracker.read().await;
                device_tracker.get_best_workers(&requirement, 1)
            };
            
            let selected_worker = if !best_workers.is_empty() && self.available_workers.contains(&best_workers[0]) {
                best_workers[0]
            } else {
                *worker
            };

            // 现在安全地获取可变引用更新任务
            if let Some(task) = self.tasks.get_mut(&task_id) {
                task.status = TaskStatus::Processing { worker: selected_worker.to_bytes() };
                task.updated_at = chrono::Utc::now().timestamp();
                task.version += 1;
                self.task_versions.insert(*task_id, task.version);
                let _ = self.data_store.save_task(&task);

                let assign_message = QueueMessage::AssignTask {
                    task_id: task_id.into_bytes(),
                    worker: selected_worker.to_bytes(),
                    version: task.version - 1,
                };
                let broadcast_message = QueueMessage::TaskStatusBroadcast { task: task.clone() };

                if let Ok(_) = self.publish_message(assign_message).await {
                    info!("基于设备信息分配任务 {} 给节点 {}", task_id, selected_worker);
                    self.available_workers.remove(&selected_worker);
                    let _ = self.publish_message(broadcast_message).await;
                }
            }
        }
    }
    
    fn save_raft_state(&self) {
        if let Err(e) = self.data_store.save_raft_state(
            self.raft_state.current_term,
            self.raft_state.voted_for
        ) {
            error!("保存Raft状态失败: {}", e);
        }
    }
    
    fn calculate_stats(&self) -> QueueStats {
        let total = self.tasks.len();
        let completed = self.tasks.values()
            .filter(|t| matches!(t.status, TaskStatus::Completed))
            .count();
        let failed = self.tasks.values()
            .filter(|t| matches!(t.status, TaskStatus::Failed { .. }))
            .count();
        
        let success_rate = if (completed + failed) > 0 {
            (completed as f64 / (completed + failed) as f64) * 100.0
        } else {
            0.0
        };
        
        let total_retries: u32 = self.tasks.values().map(|t| t.retry_count).sum();
        let avg_retry_count = if total > 0 {
            total_retries as f64 / total as f64
        } else {
            0.0
        };
        
        let mut tasks_by_type = HashMap::new();
        for task in self.tasks.values() {
            *tasks_by_type.entry(task.task_type.clone()).or_insert(0) += 1;
        }
        
        QueueStats {
            total_tasks: total,
            pending_tasks: self.tasks.values()
                .filter(|t| matches!(t.status, TaskStatus::Pending))
                .count(),
            processing_tasks: self.tasks.values()
                .filter(|t| matches!(t.status, TaskStatus::Processing { .. }))
                .count(),
            completed_tasks: completed,
            failed_tasks: failed,
            success_rate,
            avg_retry_count,
            tasks_by_type,
        }
    }

    async fn publish_message(&mut self, message: QueueMessage) -> Result<(), Box<dyn Error>> {
        let data = message.serialize()?;
        self.swarm
            .behaviour_mut()
            .gossipsub
            .publish(self.topic.clone(), data)?;
        Ok(())
    }

    async fn publish_message_with_retry(&mut self, message: QueueMessage) -> Result<(), Box<dyn Error>> {
        match self.publish_message(message.clone()).await {
            Ok(_) => Ok(()),
            Err(e) => {
                if e.to_string().contains("NoPeersSubscribedToTopic") {
                    self.message_cache.push_back(CachedMessage {
                        message,
                        retry_count: 0,
                        created_at: std::time::Instant::now(),
                    });
                    
                    if self.message_cache.len() > 1000 {
                        self.message_cache.pop_front();
                    }
                }
                Err(e)
            }
        }
    }

    async fn retry_cached_messages(&mut self) {
        let tracker = self.connection_tracker.read().await;
        if !tracker.has_subscribers() {
            return;
        }
        drop(tracker);

        let mut retry_messages = Vec::new();
        let now = std::time::Instant::now();

        while let Some(mut cached) = self.message_cache.pop_front() {
            if now.duration_since(cached.created_at) > Duration::from_secs(300) {
                continue;
            }

            match self.publish_message(cached.message.clone()).await {
                Ok(_) => debug!("成功重试发送缓存消息"),
                Err(_) => {
                    cached.retry_count += 1;
                    if cached.retry_count < 10 {
                        retry_messages.push(cached);
                    }
                }
            }
        }

        for msg in retry_messages {
            self.message_cache.push_back(msg);
        }
    }

    async fn request_sync(&mut self, _peer: PeerId) {
        let request_id = uuid::Uuid::new_v4().into_bytes();
        let message = QueueMessage::SyncRequest { 
            from: self.peer_id.into(),
            request_id,
        };
        let _ = self.publish_message(message).await;
    }

    async fn sync_tasks(&mut self) {
        let tracker = self.connection_tracker.read().await;
        if tracker.has_subscribers() {
            drop(tracker);
            let request_id = uuid::Uuid::new_v4().into_bytes();
            let message = QueueMessage::SyncRequest { 
                from: self.peer_id.into(),
                request_id,
            };
            let _ = self.publish_message(message).await;
        }
    }
    
    fn cleanup_old_messages(&mut self) {
        if self.processed_messages.len() > 10000 {
            self.processed_messages.clear();
        }
        
        let now = std::time::Instant::now();
        self.sync_requests.retain(|_, time| {
            now.duration_since(*time) < Duration::from_secs(600)
        });
    }
    
    pub async fn complete_task(&mut self, task_id: uuid::Uuid) {
        let version = self.tasks.get(&task_id).map(|task| task.version).unwrap_or(0);
        let message = QueueMessage::CompleteTask {
            task_id: task_id.into_bytes(),
            worker: self.peer_id.into(),
            version,
        };
        
        if let Err(e) = self.publish_message_with_retry(message).await {
            error!("标记任务完成失败: {}", e);
        }
    }

    pub async fn fail_task(&mut self, task_id: uuid::Uuid, reason: String) {
        let version = self.tasks.get(&task_id).map(|task| task.version).unwrap_or(0);
        let message = QueueMessage::FailTask {
            task_id: task_id.into_bytes(),
            worker: self.peer_id.into(),
            reason,
            version,
        };
        
        if let Err(e) = self.publish_message_with_retry(message).await {
            error!("标记任务失败失败: {}", e);
        }
    }

    async fn cleanup_database(&mut self) {
        let cleanup_config = CleanupConfig {
            enable_auto_cleanup: true,
            cleanup_interval: Duration::from_secs(3600),
            max_task_age_days: 30,
            max_completed_tasks: 10000,
            max_failed_tasks: 5000,
            compact_after_cleanup: true,
        };
        
        match self.data_store.cleanup_old_data(&cleanup_config) {
            Ok(stats) => {
                info!("数据库清理完成: 过期任务={}, 多余完成任务={}, 多余失败任务={}, 孤立索引={}", 
                      stats.expired_tasks, stats.excess_completed, stats.excess_failed, stats.orphaned_indexes);
                
                // 获取清理后的数据库统计信息
                if let Ok(db_stats) = self.data_store.get_database_stats() {
                    info!("数据库统计: 总任务={}, 待处理={}, 已完成={}, 大小={}MB", 
                          db_stats.total_tasks, db_stats.pending_tasks, db_stats.completed_tasks,
                          db_stats.database_size_bytes / 1024 / 1024);
                }
            }
            Err(e) => {
                error!("数据库清理失败: {}", e);
            }
        }
    }

    pub async fn update_task(
        &self, 
        task_id: uuid::Uuid, 
        task_type: Option<String>, 
        payload: Option<String>, 
        priority: Option<u16>
    ) -> Result<(), String> {
        let (tx, rx) = oneshot::channel();
        let cmd = Command::UpdateTask { 
            task_id, 
            task_type, 
            payload, 
            priority, 
            response: tx 
        };
        
        self.cmd_tx.send(cmd).map_err(|_| "队列已关闭".to_string())?;
        rx.await.map_err(|_| "接收响应失败".to_string())?
    }

    pub async fn delete_task(&self, task_id: uuid::Uuid) -> Result<(), String> {
        let (tx, rx) = oneshot::channel();
        let cmd = Command::DeleteTask { 
            task_id, 
            response: tx 
        };
        
        self.cmd_tx.send(cmd).map_err(|_| "队列已关闭".to_string())?;
        rx.await.map_err(|_| "接收响应失败".to_string())?
    }

    async fn request_all_device_info(&mut self) {
        let message = QueueMessage::DeviceInfoRequest {
            from: self.peer_id.to_bytes(),
        };
        let _ = self.publish_message(message).await;
        info!("Leader请求所有节点设备信息");
    }

    fn determine_task_requirement_by_type(&self, task_type: &str) -> TaskRequirement {
        match task_type {
            t if t.contains("gpu") || t.contains("cuda") || t.contains("ml") || t.contains("ai") => TaskRequirement::GpuIntensive,
            t if t.contains("cpu") || t.contains("compute") => TaskRequirement::CpuIntensive,
            t if t.contains("memory") || t.contains("cache") => TaskRequirement::MemoryIntensive,
            _ => TaskRequirement::Balanced,
        }
    }
}
