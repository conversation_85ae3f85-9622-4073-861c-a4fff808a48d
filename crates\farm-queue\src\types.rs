use libp2p::PeerId;
use tokio::sync::oneshot;
use std::collections::{HashMap, HashSet};
use sysinfo::System;
use serde::{Serialize, Deserialize};

use crate::messages;

#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub enum TaskStatus {
    Pending,
    Processing { worker: Vec<u8> },
    Completed,
    Failed { reason: String },
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Task {
    pub id: [u8; 16],
    pub task_type: String,
    pub priority: u16,
    pub payload: String,
    pub status: TaskStatus,
    pub created_at: i64,
    pub updated_at: i64,
    pub retry_count: u32,
    pub max_retries: u32,
    pub version: u64,
}

impl Task {
    pub fn new(task_type: String, payload: String) -> Self {
        let now = chrono::Utc::now();
        Self {
            id: *uuid::Uuid::new_v4().as_bytes(),
            task_type,
            priority: 0,
            payload,
            status: TaskStatus::Pending,
            created_at: now.timestamp(),
            updated_at: now.timestamp(),
            retry_count: 0,
            max_retries: 3,
            version: 0,
        }
    }
    
    pub fn with_priority(task_type: String, payload: String, priority: u16) -> Self {
        let mut task = Self::new(task_type, payload);
        task.priority = priority;
        task
    }
}

#[derive(Debug, Clone)]
pub struct QueueStats {
    pub total_tasks: usize,
    pub pending_tasks: usize,
    pub processing_tasks: usize,
    pub completed_tasks: usize,
    pub failed_tasks: usize,
    pub success_rate: f64,
    pub avg_retry_count: f64,
    pub tasks_by_type: HashMap<String, usize>,
}

#[derive(Debug, Clone)]
pub struct LeaderInfo {
    pub is_leader: bool,
    pub leader_id: Option<PeerId>,
    pub current_term: u64,
    pub state: String,
}

#[derive(Debug, Default)]
pub struct ConnectionTracker {
    pub connected_peers: HashSet<PeerId>,
    pub subscribed_peers: HashSet<PeerId>,
    pub last_seen: HashMap<PeerId, std::time::Instant>,
}

impl ConnectionTracker {
    pub fn add_peer(&mut self, peer_id: PeerId) {
        self.connected_peers.insert(peer_id);
        self.last_seen.insert(peer_id, std::time::Instant::now());
    }

    pub fn remove_peer(&mut self, peer_id: &PeerId) {
        self.connected_peers.remove(peer_id);
        self.subscribed_peers.remove(peer_id);
        self.last_seen.remove(peer_id);
    }

    pub fn mark_subscribed(&mut self, peer_id: PeerId) {
        self.subscribed_peers.insert(peer_id);
    }

    pub fn has_subscribers(&self) -> bool {
        !self.subscribed_peers.is_empty()
    }

    pub fn get_subscribed_count(&self) -> usize {
        self.subscribed_peers.len()
    }
}

#[derive(Debug, Clone)]
pub struct CachedMessage {
    pub message: messages::proto::QueueMessage,
    pub retry_count: u32,
    pub created_at: std::time::Instant,
}

#[derive(Debug, Default, Clone)]
pub struct DatabaseStats {
    pub total_tasks: usize,
    pub pending_tasks: usize,
    pub completed_tasks: usize,
    pub database_size_bytes: u64,
}

#[derive(Debug)]
pub enum Command {
    SubmitTask { task_type: String, payload: String, response: oneshot::Sender<Result<uuid::Uuid, String>> },
    UpdateTask { task_id: uuid::Uuid, task_type: Option<String>, payload: Option<String>, priority: Option<u16>, response: oneshot::Sender<Result<(), String>> },
    DeleteTask { task_id: uuid::Uuid, response: oneshot::Sender<Result<(), String>> },
    ProcessNextTask { response: oneshot::Sender<Option<Task>> },
    GetTaskStatus { task_id: uuid::Uuid, response: oneshot::Sender<Option<Task>> },
    GetConnectedPeers { response: oneshot::Sender<Vec<PeerId>> },
    GetQueueStats { response: oneshot::Sender<QueueStats> },
    GetLeaderInfo { response: oneshot::Sender<LeaderInfo> },
    CompleteTask { task_id: uuid::Uuid },
    FailTask { task_id: uuid::Uuid, reason: String },
    CleanupDatabase,
    GetDatabaseStats,
    ElectionTimeout { term: u64 },
    GetAllTasks { response: oneshot::Sender<Result<Vec<Task>, String>> },
    GetAllDeviceInfo { response: oneshot::Sender<Result<HashMap<PeerId, DeviceInfo>, String>> },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub device_name: String,
    pub username: String,
    pub cpu_cores: usize,
    pub cpu_frequency: u64, // MHz
    pub cpu_usage: f32,     // percentage
    pub total_memory: u64,  // bytes
    pub available_memory: u64, // bytes
    pub used_memory: u64,   // bytes
    pub gpu_info: Vec<GpuInfo>,
    pub last_updated: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GpuInfo {
    pub name: String,
    pub total_memory: u64,    // bytes
    pub available_memory: u64, // bytes
    pub core_count: u32,      // CUDA cores or compute units
    pub usage: f32,           // percentage
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TaskRequirement {
    CpuIntensive,
    MemoryIntensive,
    GpuIntensive,
    Balanced,
}

impl DeviceInfo {
    pub fn collect() -> Result<Self, Box<dyn std::error::Error>> {
        let mut sys = System::new_all();
        sys.refresh_all();

        let device_name = System::host_name().unwrap_or_else(|| "Unknown".to_string());
        let username = whoami::username();

        let cpu = sys.global_cpu_info();
        let cpu_cores = sys.cpus().len();
        let cpu_frequency = cpu.frequency();
        let cpu_usage = cpu.cpu_usage();

        let total_memory = sys.total_memory();
        let available_memory = sys.available_memory();
        let used_memory = sys.used_memory();

        // 获取GPU信息
        let gpu_info = Self::collect_gpu_info();

        Ok(DeviceInfo {
            device_name,
            username,
            cpu_cores,
            cpu_frequency,
            cpu_usage,
            total_memory,
            available_memory,
            used_memory,
            gpu_info,
            last_updated: chrono::Utc::now().timestamp(),
        })
    }

    fn collect_gpu_info() -> Vec<GpuInfo> {
        let mut gpu_info = Vec::new();
        
        // 尝试通过nvidia-ml-py获取NVIDIA GPU信息
        if let Ok(output) = std::process::Command::new("nvidia-smi")
            .args(&["--query-gpu=name,memory.total,memory.free,utilization.gpu", "--format=csv,noheader,nounits"])
            .output() 
        {
            if output.status.success() {
                let output_str = String::from_utf8_lossy(&output.stdout);
                for line in output_str.lines() {
                    let parts: Vec<&str> = line.split(',').map(|s| s.trim()).collect();
                    if parts.len() >= 4 {
                        if let (Ok(total_mem), Ok(free_mem), Ok(usage)) = (
                            parts[1].parse::<u64>(),
                            parts[2].parse::<u64>(),
                            parts[3].parse::<f32>()
                        ) {
                            gpu_info.push(GpuInfo {
                                name: parts[0].to_string(),
                                total_memory: total_mem * 1024 * 1024, // MB to bytes
                                available_memory: free_mem * 1024 * 1024, // MB to bytes
                                core_count: Self::estimate_cuda_cores(&parts[0]),
                                usage,
                            });
                        }
                    }
                }
            }
        }

        // 如果没有找到GPU，返回空列表
        gpu_info
    }

    fn estimate_cuda_cores(gpu_name: &str) -> u32 {
        // 根据GPU名称估算CUDA核心数（简化版本）
        let name_lower = gpu_name.to_lowercase();
        if name_lower.contains("rtx 4090") { 16384 }
        else if name_lower.contains("rtx 4080") { 9728 }
        else if name_lower.contains("rtx 4070") { 5888 }
        else if name_lower.contains("rtx 3090") { 10496 }
        else if name_lower.contains("rtx 3080") { 8704 }
        else if name_lower.contains("rtx 3070") { 5888 }
        else if name_lower.contains("gtx 1080") { 2560 }
        else if name_lower.contains("gtx 1070") { 1920 }
        else { 1024 } // 默认值
    }

    pub fn calculate_score(&self, requirement: &TaskRequirement) -> f32 {
        match requirement {
            TaskRequirement::CpuIntensive => {
                // CPU分数 = 核心数 * 频率(GHz) * 可用率
                let available_cpu_ratio = (100.0 - self.cpu_usage) / 100.0;
                let cpu_score = self.cpu_cores as f32 * (self.cpu_frequency as f32 / 1000.0) * available_cpu_ratio;
                cpu_score
            },
            TaskRequirement::MemoryIntensive => {
                self.available_memory as f32 / (1024.0 * 1024.0 * 1024.0) // GB
            },
            TaskRequirement::GpuIntensive => {
                // GPU分数 = 所有GPU的(核心数 * 可用显存GB * 可用率)之和
                self.gpu_info.iter().map(|gpu| {
                    let available_gpu_ratio = (100.0 - gpu.usage) / 100.0;
                    let gpu_memory_gb = gpu.available_memory as f32 / (1024.0 * 1024.0 * 1024.0);
                    gpu.core_count as f32 * gpu_memory_gb * available_gpu_ratio / 1000.0 // 缩放因子
                }).sum()
            },
            TaskRequirement::Balanced => {
                let available_cpu_ratio = (100.0 - self.cpu_usage) / 100.0;
                let cpu_score = self.cpu_cores as f32 * available_cpu_ratio;
                let memory_score = self.available_memory as f32 / (1024.0 * 1024.0 * 1024.0);
                let gpu_score = self.gpu_info.iter().map(|gpu| {
                    let available_gpu_ratio = (100.0 - gpu.usage) / 100.0;
                    gpu.core_count as f32 * available_gpu_ratio / 1000.0
                }).sum::<f32>();
                
                (cpu_score + memory_score + gpu_score) / 4.0
            }
        }
    }

    pub fn get_total_gpu_memory(&self) -> u64 {
        self.gpu_info.iter().map(|gpu| gpu.total_memory).sum()
    }

    pub fn get_available_gpu_memory(&self) -> u64 {
        self.gpu_info.iter().map(|gpu| gpu.available_memory).sum()
    }

    pub fn get_total_gpu_cores(&self) -> u32 {
        self.gpu_info.iter().map(|gpu| gpu.core_count).sum()
    }
}

pub struct DeviceTracker {
    pub device_info: HashMap<PeerId, DeviceInfo>,
}

impl Default for DeviceTracker {
    fn default() -> Self {
        Self {
            device_info: HashMap::new(),
        }
    }
}

impl DeviceTracker {
    pub fn update_device_info(&mut self, peer_id: PeerId, info: DeviceInfo) {
        self.device_info.insert(peer_id, info);
    }

    pub fn remove_device_info(&mut self, peer_id: &PeerId) {
        self.device_info.remove(peer_id);
    }

    pub fn get_best_workers(&self, requirement: &TaskRequirement, count: usize) -> Vec<PeerId> {
        let mut scored_workers: Vec<(PeerId, f32)> = self.device_info
            .iter()
            .map(|(peer_id, device_info)| (*peer_id, device_info.calculate_score(requirement)))
            .collect();

        scored_workers.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        scored_workers.into_iter().take(count).map(|(peer_id, _)| peer_id).collect()
    }

    pub fn has_device_info(&self, peer_id: &PeerId) -> bool {
        self.device_info.contains_key(peer_id)
    }

    pub fn get_device_count(&self) -> usize {
        self.device_info.len()
    }
}

// 为了与 protobuf 消息兼容的辅助方法
impl TaskStatus {
    pub fn status_string(&self) -> &'static str {
        match self {
            TaskStatus::Pending => "pending",
            TaskStatus::Processing { .. } => "processing",
            TaskStatus::Completed => "completed",
            TaskStatus::Failed { .. } => "failed",
        }
    }
}