{"$message_type":"diagnostic","message":"unused variable: `term`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\farm-queue\\src\\queue.rs","byte_start":11426,"byte_end":11430,"line_start":274,"line_end":274,"column_start":40,"column_end":44,"is_primary":true,"text":[{"text":"            Command::ElectionTimeout { term } => {","highlight_start":40,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"crates\\farm-queue\\src\\queue.rs","byte_start":11426,"byte_end":11430,"line_start":274,"line_end":274,"column_start":40,"column_end":44,"is_primary":true,"text":[{"text":"            Command::ElectionTimeout { term } => {","highlight_start":40,"highlight_end":44}],"label":null,"suggested_replacement":"term: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `term`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\farm-queue\\src\\queue.rs:274:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Command::ElectionTimeout { term } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: try ignoring the field: `term: _`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `proposer_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\farm-queue\\src\\queue.rs","byte_start":39245,"byte_end":39256,"line_start":837,"line_end":837,"column_start":19,"column_end":30,"is_primary":true,"text":[{"text":"        if let Ok(proposer_id) = PeerId::from_bytes(&propose.proposer_id) {","highlight_start":19,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\farm-queue\\src\\queue.rs","byte_start":39245,"byte_end":39256,"line_start":837,"line_end":837,"column_start":19,"column_end":30,"is_primary":true,"text":[{"text":"        if let Ok(proposer_id) = PeerId::from_bytes(&propose.proposer_id) {","highlight_start":19,"highlight_end":30}],"label":null,"suggested_replacement":"_proposer_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `proposer_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\farm-queue\\src\\queue.rs:837:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m837\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Ok(proposer_id) = PeerId::from_bytes(&propose.proposer_id) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_proposer_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot use `self.node_id` because it was mutably borrowed","code":{"code":"E0503","explanation":"A value was used after it was mutably borrowed.\n\nErroneous code example:\n\n```compile_fail,E0503\nfn main() {\n    let mut value = 3;\n    // Create a mutable borrow of `value`.\n    let borrow = &mut value;\n    let _sum = value + 1; // error: cannot use `value` because\n                          //        it was mutably borrowed\n    println!(\"{}\", borrow);\n}\n```\n\nIn this example, `value` is mutably borrowed by `borrow` and cannot be\nused to calculate `sum`. This is not possible because this would violate\nRust's mutability rules.\n\nYou can fix this error by finishing using the borrow before the next use of\nthe value:\n\n```\nfn main() {\n    let mut value = 3;\n    let borrow = &mut value;\n    println!(\"{}\", borrow);\n    // The block has ended and with it the borrow.\n    // You can now use `value` again.\n    let _sum = value + 1;\n}\n```\n\nOr by cloning `value` before borrowing it:\n\n```\nfn main() {\n    let mut value = 3;\n    // We clone `value`, creating a copy.\n    let value_cloned = value.clone();\n    // The mutable borrow is a reference to `value` and\n    // not to `value_cloned`...\n    let borrow = &mut value;\n    // ... which means we can still use `value_cloned`,\n    let _sum = value_cloned + 1;\n    // even though the borrow only ends here.\n    println!(\"{}\", borrow);\n}\n```\n\nFor more information on Rust's ownership system, take a look at the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n"},"level":"error","spans":[{"file_name":"crates\\farm-queue\\src\\paxos.rs","byte_start":7922,"byte_end":7926,"line_start":232,"line_end":232,"column_start":24,"column_end":28,"is_primary":false,"text":[{"text":"        let instance = self.get_or_create_instance(prepare.sequence);","highlight_start":24,"highlight_end":28}],"label":"`*self` is borrowed here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\farm-queue\\src\\paxos.rs","byte_start":8410,"byte_end":8422,"line_start":242,"line_end":242,"column_start":30,"column_end":42,"is_primary":true,"text":[{"text":"                acceptor_id: self.node_id.to_bytes(),","highlight_start":30,"highlight_end":42}],"label":"use of borrowed `*self`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\farm-queue\\src\\paxos.rs","byte_start":8476,"byte_end":8502,"line_start":243,"line_end":243,"column_start":41,"column_end":67,"is_primary":false,"text":[{"text":"                last_accepted_proposal: instance.accepted_proposal,","highlight_start":41,"highlight_end":67}],"label":"borrow later used here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0503]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot use `self.node_id` because it was mutably borrowed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\farm-queue\\src\\paxos.rs:242:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let instance = self.get_or_create_instance(prepare.sequence);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`*self` is borrowed here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m242\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                acceptor_id: self.node_id.to_bytes(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of borrowed `*self`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m243\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                last_accepted_proposal: instance.accepted_proposal,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mborrow later used here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 2 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error; 2 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0503`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0503`.\u001b[0m\n"}
