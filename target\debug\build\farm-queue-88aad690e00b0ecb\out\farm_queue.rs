// This file is @generated by prost-build.
/// 任务状态
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TaskStatus {
    #[prost(enumeration = "TaskStatusType", tag = "1")]
    pub status_type: i32,
    /// 仅在PROCESSING状态时使用
    #[prost(bytes = "vec", tag = "2")]
    pub worker: ::prost::alloc::vec::Vec<u8>,
    /// 仅在FAILED状态时使用
    #[prost(string, tag = "3")]
    pub reason: ::prost::alloc::string::String,
}
/// 任务
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Task {
    /// UUID as bytes\[16\]
    #[prost(bytes = "vec", tag = "1")]
    pub id: ::prost::alloc::vec::Vec<u8>,
    #[prost(string, tag = "2")]
    pub task_type: ::prost::alloc::string::String,
    #[prost(uint32, tag = "3")]
    pub priority: u32,
    #[prost(string, tag = "4")]
    pub payload: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "5")]
    pub status: ::core::option::Option<TaskStatus>,
    #[prost(int64, tag = "6")]
    pub created_at: i64,
    #[prost(int64, tag = "7")]
    pub updated_at: i64,
    #[prost(uint32, tag = "8")]
    pub retry_count: u32,
    #[prost(uint32, tag = "9")]
    pub max_retries: u32,
    #[prost(uint64, tag = "10")]
    pub version: u64,
}
/// 设备GPU信息
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GpuInfo {
    #[prost(string, tag = "1")]
    pub name: ::prost::alloc::string::String,
    #[prost(uint64, tag = "2")]
    pub total_memory: u64,
    #[prost(uint64, tag = "3")]
    pub available_memory: u64,
    #[prost(uint32, tag = "4")]
    pub core_count: u32,
    #[prost(float, tag = "5")]
    pub usage: f32,
}
/// 设备信息
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeviceInfo {
    #[prost(string, tag = "1")]
    pub device_name: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub username: ::prost::alloc::string::String,
    #[prost(uint64, tag = "3")]
    pub cpu_cores: u64,
    #[prost(uint64, tag = "4")]
    pub cpu_frequency: u64,
    #[prost(float, tag = "5")]
    pub cpu_usage: f32,
    #[prost(uint64, tag = "6")]
    pub total_memory: u64,
    #[prost(uint64, tag = "7")]
    pub available_memory: u64,
    #[prost(uint64, tag = "8")]
    pub used_memory: u64,
    #[prost(message, repeated, tag = "9")]
    pub gpu_info: ::prost::alloc::vec::Vec<GpuInfo>,
    #[prost(int64, tag = "10")]
    pub last_updated: i64,
}
/// 队列消息类型
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct QueueMessage {
    #[prost(
        oneof = "queue_message::Message",
        tags = "1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17"
    )]
    pub message: ::core::option::Option<queue_message::Message>,
}
/// Nested message and enum types in `QueueMessage`.
pub mod queue_message {
    #[allow(clippy::derive_partial_eq_without_eq)]
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Message {
        /// 任务消息
        #[prost(message, tag = "1")]
        NewTask(super::NewTaskMessage),
        #[prost(message, tag = "2")]
        UpdateTask(super::UpdateTaskMessage),
        #[prost(message, tag = "3")]
        DeleteTask(super::DeleteTaskMessage),
        #[prost(message, tag = "4")]
        AssignTask(super::AssignTaskMessage),
        #[prost(message, tag = "5")]
        CompleteTask(super::CompleteTaskMessage),
        #[prost(message, tag = "6")]
        FailTask(super::FailTaskMessage),
        #[prost(message, tag = "7")]
        RequestTask(super::RequestTaskMessage),
        /// 同步消息
        #[prost(message, tag = "8")]
        Heartbeat(super::HeartbeatMessage),
        #[prost(message, tag = "9")]
        QueryTaskStatus(super::QueryTaskStatusMessage),
        #[prost(message, tag = "10")]
        TaskStatusResponse(super::TaskStatusResponseMessage),
        #[prost(message, tag = "11")]
        SyncRequest(super::SyncRequestMessage),
        #[prost(message, tag = "12")]
        SyncResponse(super::SyncResponseMessage),
        #[prost(message, tag = "13")]
        TaskStatusBroadcast(super::TaskStatusBroadcastMessage),
        #[prost(message, tag = "14")]
        HashRequest(super::HashRequestMessage),
        #[prost(message, tag = "15")]
        HashResponse(super::HashResponseMessage),
        /// 设备信息消息
        #[prost(message, tag = "16")]
        DeviceInfoRequest(super::DeviceInfoRequestMessage),
        #[prost(message, tag = "17")]
        DeviceInfoResponse(super::DeviceInfoResponseMessage),
    }
}
/// 各种消息类型的定义
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct NewTaskMessage {
    #[prost(message, optional, tag = "1")]
    pub task: ::core::option::Option<Task>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateTaskMessage {
    #[prost(message, optional, tag = "1")]
    pub task: ::core::option::Option<Task>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteTaskMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub task_id: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "2")]
    pub version: u64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AssignTaskMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub task_id: ::prost::alloc::vec::Vec<u8>,
    #[prost(bytes = "vec", tag = "2")]
    pub worker: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "3")]
    pub version: u64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CompleteTaskMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub task_id: ::prost::alloc::vec::Vec<u8>,
    #[prost(bytes = "vec", tag = "2")]
    pub worker: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "3")]
    pub version: u64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct FailTaskMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub task_id: ::prost::alloc::vec::Vec<u8>,
    #[prost(bytes = "vec", tag = "2")]
    pub worker: ::prost::alloc::vec::Vec<u8>,
    #[prost(string, tag = "3")]
    pub reason: ::prost::alloc::string::String,
    #[prost(uint64, tag = "4")]
    pub version: u64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RequestTaskMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub worker: ::prost::alloc::vec::Vec<u8>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct HeartbeatMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub leader_id: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "2")]
    pub term: u64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct QueryTaskStatusMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub task_id: ::prost::alloc::vec::Vec<u8>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TaskStatusResponseMessage {
    #[prost(message, optional, tag = "1")]
    pub task: ::core::option::Option<Task>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SyncRequestMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub from: ::prost::alloc::vec::Vec<u8>,
    #[prost(bytes = "vec", tag = "2")]
    pub request_id: ::prost::alloc::vec::Vec<u8>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SyncResponseMessage {
    #[prost(message, repeated, tag = "1")]
    pub tasks: ::prost::alloc::vec::Vec<Task>,
    #[prost(bytes = "vec", tag = "2")]
    pub request_id: ::prost::alloc::vec::Vec<u8>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TaskStatusBroadcastMessage {
    #[prost(message, optional, tag = "1")]
    pub task: ::core::option::Option<Task>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct HashRequestMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub from: ::prost::alloc::vec::Vec<u8>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct HashResponseMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub from: ::prost::alloc::vec::Vec<u8>,
    /// key is hex-encoded task_id
    #[prost(map = "string, uint64", tag = "2")]
    pub task_hashes: ::std::collections::HashMap<::prost::alloc::string::String, u64>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeviceInfoRequestMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub from: ::prost::alloc::vec::Vec<u8>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeviceInfoResponseMessage {
    #[prost(bytes = "vec", tag = "1")]
    pub peer_id: ::prost::alloc::vec::Vec<u8>,
    #[prost(message, optional, tag = "2")]
    pub device_info: ::core::option::Option<DeviceInfo>,
}
/// Raft状态持久化
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RaftState {
    #[prost(uint64, tag = "1")]
    pub current_term: u64,
    /// Optional PeerId
    #[prost(bytes = "vec", tag = "2")]
    pub voted_for: ::prost::alloc::vec::Vec<u8>,
}
/// 任务状态枚举
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum TaskStatusType {
    Pending = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
}
impl TaskStatusType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            TaskStatusType::Pending => "PENDING",
            TaskStatusType::Processing => "PROCESSING",
            TaskStatusType::Completed => "COMPLETED",
            TaskStatusType::Failed => "FAILED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "PENDING" => Some(Self::Pending),
            "PROCESSING" => Some(Self::Processing),
            "COMPLETED" => Some(Self::Completed),
            "FAILED" => Some(Self::Failed),
            _ => None,
        }
    }
}
