{"rustc": 14799578172954012516, "features": "[\"default\", \"format\", \"prettyplease\", \"syn\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"format\", \"prettyplease\", \"pulldown-cmark\", \"pulldown-cmark-to-cmark\", \"syn\"]", "target": 5767990241175469825, "profile": 2225463790103693989, "path": 15364954725173924580, "deps": [[1441306149310335789, "tempfile", false, 6188602290492039271], [2713742371683562785, "syn", false, 11856977537046708234], [5986029879202738730, "log", false, 10740869240818191808], [6243494903393190189, "which", false, 4711321339979096592], [6731423069546993303, "prost_types", false, 2066885912402465133], [8045585743974080694, "heck", false, 14432731811692958784], [8107795630852386116, "prost", false, 15671820015482943955], [9451456094439810778, "regex", false, 10664981519166830513], [11176364336753726272, "prettyplease", false, 13360794093726073163], [11903278875415370753, "itertools", false, 607881722412436650], [14643244732240593859, "multimap", false, 6372456072275091913], [16066129441945555748, "bytes", false, 12803768404556351651], [16532555906320553198, "petgraph", false, 7507471856218154931], [17917672826516349275, "lazy_static", false, 8078700589956690812]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\prost-build-aac2e9856b80ae04\\dep-lib-prost_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}