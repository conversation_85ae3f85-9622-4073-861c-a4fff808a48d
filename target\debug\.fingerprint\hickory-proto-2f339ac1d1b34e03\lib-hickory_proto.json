{"rustc": 14799578172954012516, "features": "[\"futures-io\", \"mdns\", \"socket2\", \"std\"]", "declared_features": "[\"__dnssec\", \"__h3\", \"__https\", \"__quic\", \"__tls\", \"backtrace\", \"default\", \"dnssec-aws-lc-rs\", \"dnssec-ring\", \"futures-io\", \"h3-aws-lc-rs\", \"h3-ring\", \"https-aws-lc-rs\", \"https-ring\", \"mdns\", \"no-std-rand\", \"quic-aws-lc-rs\", \"quic-ring\", \"rustls-platform-verifier\", \"serde\", \"socket2\", \"std\", \"testing\", \"text-parsing\", \"tls-aws-lc-rs\", \"tls-ring\", \"tokio\", \"wasm-bindgen\", \"webpki-roots\"]", "target": 845874235032908426, "profile": 16526098328036766351, "path": 16140619076304055928, "deps": [[5103565458935487, "futures_io", false, 9170550380918285218], [95042085696191081, "ipnet", false, 888231073648350077], [99287295355353247, "data_encoding", false, 14304986939621533895], [1042707345065476716, "tinyvec", false, 8968729162674231294], [1811549171721445101, "futures_channel", false, 14244961674563275624], [2828590642173593838, "cfg_if", false, 14149674807569560563], [3150220818285335163, "url", false, 10730902746554352453], [3722963349756955755, "once_cell", false, 13393876230255124741], [6376232718484714452, "idna", false, 7576799531229449634], [8606274917505247608, "tracing", false, 11094703943940771815], [10399806393418013851, "enum_as_inner", false, 11798119369940292940], [10629569228670356391, "futures_util", false, 839015174733518877], [10806645703491011684, "thiserror", false, 12398725825002675698], [11916940916964035392, "rand", false, 9286351777454675946], [11946729385090170470, "async_trait", false, 17971399228253674846], [12614995553916589825, "socket2", false, 10436837080195249994]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hickory-proto-2f339ac1d1b34e03\\dep-lib-hickory_proto", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}