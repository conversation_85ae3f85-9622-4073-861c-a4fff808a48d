{"rustc": 14799578172954012516, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[3430646239657634944, "build_script_build", false, 11185873170729080259]], "local": [{"RerunIfChanged": {"output": "debug\\build\\rustix-8cb415d4c8396a7f\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_EXPERIMENTAL_ASM", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_RUSTC_DEP_OF_STD", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_MIRI", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}