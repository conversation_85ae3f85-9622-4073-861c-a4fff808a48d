{"rustc": 14799578172954012516, "features": "[\"default\", \"extension-module\", \"resolve-config\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"python3-dll-a\", \"resolve-config\"]", "target": 11662020830826191376, "profile": 2225463790103693989, "path": 14589284523024904940, "deps": [[3722963349756955755, "once_cell", false, 7371228774400925526], [10296317077653712691, "target_lexicon", false, 209584011441805162], [11057642215492122650, "build_script_build", false, 4721011653784134065]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-build-config-88ef52f6c1f45806\\dep-lib-pyo3_build_config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}